{"name": "adtip", "version": "33.0.0", "private": true, "scripts": {"test": "node ../testing/scripts/runAllTests.js", "test:unit": "jest ../testing/unit --config ../testing/jest.config.js", "test:integration": "jest ../testing/integration --config ../testing/jest.config.js", "test:performance": "jest ../testing/performance --config ../testing/jest.config.js", "test:e2e": "detox test --configuration ios.sim.debug", "test:coverage": "jest ../testing/unit ../testing/integration --coverage --config ../testing/jest.config.js", "test:watch": "jest ../testing/unit --watch --config ../testing/jest.config.js", "test:ci": "node ../testing/scripts/runAllTests.js --ci", "android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "postinstall": "patch-package"}, "dependencies": {"@aws-sdk/client-s3": "^3.839.0", "@aws-sdk/s3-request-presigner": "^3.839.0", "@babel/plugin-transform-class-static-block": "^7.27.1", "@d11/react-native-fast-image": "^8.9.2", "@good-react-native/keyboard-avoider": "^1.1.4", "@notifee/react-native": "^9.1.8", "@nozbe/watermelondb": "^0.28.0", "@nozbe/with-observables": "^1.6.0", "@playwright/mcp": "^0.0.30", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-clipboard/clipboard": "^1.16.3", "@react-native-community/blur": "^4.4.1", "@react-native-community/datetimepicker": "^8.4.1", "@react-native-community/masked-view": "^0.1.11", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/progress-bar-android": "^1.0.5", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-firebase/analytics": "^22.2.1", "@react-native-firebase/app": "^22.2.1", "@react-native-firebase/auth": "^22.2.1", "@react-native-firebase/crashlytics": "^22.2.1", "@react-native-firebase/database": "^22.2.1", "@react-native-firebase/firestore": "^22.2.1", "@react-native-firebase/messaging": "^22.2.1", "@react-native-firebase/storage": "^22.2.1", "@react-native-oh-tpl/react-native-image-zoom-viewer": "^3.0.1-0.0.1", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.14", "@react-navigation/stack": "^7.3.3", "@shopify/react-native-skia": "^2.0.3", "@tanstack/react-query": "^5.81.2", "@types/lodash.debounce": "^4.0.9", "@videosdk.live/react-native-sdk": "^0.3.7", "@videosdk.live/react-native-webrtc": "^0.0.19", "@xstate/react": "^6.0.0", "axios": "^1.9.0", "cpx-research-sdk-react-native": "^2.0.25", "date-fns": "^4.1.0", "deep-equal": "^2.2.3", "expo-file-system": "^18.1.10", "immer": "^9.0.21", "lottie-react-native": "^7.2.2", "lucide-react-native": "^0.513.0", "moment": "^2.30.1", "nativewind": "^4.1.23", "patch-package": "^8.0.0", "playwright": "^1.54.1", "playwright-core": "^1.54.1", "postinstall-postinstall": "^2.1.0", "react": "19.0.0", "react-native": "0.79.2", "react-native-callkeep": "^4.3.16", "react-native-compressor": "^1.11.0", "react-native-country-codes-picker": "^2.3.5", "react-native-create-thumbnail": "^2.1.1", "react-native-css-interop": "^0.1.22", "react-native-date-picker": "^5.0.12", "react-native-dropdown-picker": "^5.4.6", "react-native-event-listeners": "^1.0.7", "react-native-fs": "^2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.26.0", "react-native-get-random-values": "^1.11.0", "react-native-gifted-charts": "^1.4.61", "react-native-google-mobile-ads": "^15.3.1", "react-native-google-places-autocomplete": "^2.5.7", "react-native-image-crop-picker": "^0.50.1", "react-native-image-picker": "^8.2.1", "react-native-linear-gradient": "^2.8.3", "react-native-netinfo": "^1.1.0", "react-native-orientation-locker": "^1.7.0", "react-native-otp-textinput": "^1.1.7", "react-native-pager-view": "^6.8.1", "react-native-permissions": "^5.4.1", "react-native-progress": "^5.0.1", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "^3.18.0", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-skeleton-placeholder": "^5.2.4", "react-native-svg": "^15.12.0", "react-native-toast-message": "^2.3.0", "react-native-url-polyfill": "^2.0.0", "react-native-uuid": "^2.0.3", "react-native-vector-icons": "^10.2.0", "react-native-video": "^6.14.1", "react-native-view-shot": "^4.0.3", "react-native-vision-camera": "^4.7.0", "react-native-voip-push-notification": "^3.3.3", "react-native-webview": "^13.14.1", "react-redux": "^9.2.0", "redux": "^5.0.1", "rxjs": "^7.8.2", "socket.io-client": "^4.8.1", "tailwindcss": "^3.4.17", "use-immer": "^0.7.0", "victory-native": "^41.17.3", "xstate": "^5.20.1", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-decorators": "^7.28.0", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.2", "@react-native/eslint-config": "0.79.2", "@react-native/metro-config": "0.79.2", "@react-native/typescript-config": "0.79.2", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "eslint-formatter-codeframe": "^7.32.1", "eslint-plugin-prettier": "^5.4.1", "jest": "^29.6.3", "prettier": "^3.5.3", "react-test-renderer": "19.0.0", "typescript": "^5.0.4", "typescript-eslint": "^8.36.0"}, "engines": {"node": ">=18"}}