# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 55ms
  generate-prefab-packages
    exec-prefab 511ms
    [gap of 11ms]
  generate-prefab-packages completed in 528ms
  execute-generate-process
    exec-configure 540ms
    [gap of 110ms]
  execute-generate-process completed in 650ms
  remove-unexpected-so-files 16ms
  [gap of 16ms]
generate_cxx_metadata completed in 1267ms

