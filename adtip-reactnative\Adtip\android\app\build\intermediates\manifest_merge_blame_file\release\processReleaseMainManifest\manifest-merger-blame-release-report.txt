1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.adtip.app.adtip_app"
4    android:versionCode="30004"
5    android:versionName="33.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- BASIC NETWORK PERMISSIONS -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:5:5-67
12-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:5:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:6:5-79
13-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:6:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:7:5-76
14-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:7:22-73
15
16    <!-- WHATSAPP-LIKE CALLING PERMISSIONS - Minimal set -->
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
17-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:10:5-77
17-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:10:22-74
18    <uses-permission android:name="android.permission.RECORD_AUDIO" />
18-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:11:5-71
18-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:11:22-68
19    <uses-permission android:name="android.permission.CAMERA" />
19-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:12:5-65
19-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:12:22-62
20    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
20-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:13:5-80
20-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:13:22-77
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
21-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:14:5-120
21-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:14:22-85
22    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CAMERA" />
22-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:15:5-116
22-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:15:22-81
23
24    <!-- VIDEOSDK & OVERLAY PERMISSIONS -->
25    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
25-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:18:5-78
25-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:18:22-75
26    <uses-permission android:name="android.permission.WAKE_LOCK" />
26-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:19:5-68
26-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:19:22-65
27
28    <!-- BLUETOOTH PERMISSIONS -->
29    <uses-permission
29-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:22:5-95
30        android:name="android.permission.BLUETOOTH"
30-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:22:22-65
31        android:maxSdkVersion="30" />
31-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:22:66-92
32    <uses-permission
32-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:23:5-101
33        android:name="android.permission.BLUETOOTH_ADMIN"
33-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:23:22-71
34        android:maxSdkVersion="30" />
34-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:23:72-98
35    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
35-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:24:5-76
35-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:24:22-73
36    <uses-permission
36-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:25:5-27:44
37        android:name="android.permission.BLUETOOTH_SCAN"
37-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:25:22-70
38        android:usesPermissionFlags="neverForLocation" />
38-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:26:22-68
39
40    <!-- LOCATION PERMISSIONS -->
41    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
41-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:30:5-79
41-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:30:22-76
42    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
42-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:31:5-81
42-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:31:22-78
43
44    <!-- STORAGE PERMISSIONS -->
45    <uses-permission
45-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:34:5-107
46        android:name="android.permission.READ_EXTERNAL_STORAGE"
46-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:34:22-77
47        android:maxSdkVersion="32" />
47-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:34:78-104
48    <uses-permission
48-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:35:5-39:24
49        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
49-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:35:22-78
50        android:maxSdkVersion="28" />
50-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:36:22-48
51    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
51-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:40:5-102
51-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:40:22-72
52    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
52-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:41:5-102
52-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:41:22-72
53    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
53-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:42:5-103
53-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:42:22-73
54
55    <!-- NOTIFICATIONS -->
56    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
56-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:45:5-104
56-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:45:22-74
57    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
57-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:46:5-81
57-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:46:22-78
58    <uses-permission android:name="android.permission.VIBRATE" />
58-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:47:5-66
58-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:47:22-63
59    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
59-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:48:5-88
59-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:48:22-85
60    <uses-permission android:name="android.permission.MANAGE_OWN_CALLS" />
60-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:49:5-75
60-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:49:22-72
61
62    <!-- CALLLEEP SELF-MANAGED PERMISSIONS -->
63    <uses-permission android:name="android.permission.BIND_TELECOM_CONNECTION_SERVICE" />
63-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:52:5-90
63-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:52:22-87
64    <uses-permission android:name="android.permission.CALL_PHONE" />
64-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:53:5-69
64-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:53:22-66
65    <!-- ADD_VOICEMAIL removed - not used by core app functionality -->
66    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
66-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:55:5-81
66-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:55:22-78
67
68    <!-- Camera features -->
69    <uses-feature
69-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:58:5-85
70        android:name="android.hardware.camera"
70-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:58:19-57
71        android:required="false" />
71-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:58:58-82
72    <uses-feature
72-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:59:5-95
73        android:name="android.hardware.camera.autofocus"
73-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:59:19-67
74        android:required="false" />
74-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:59:68-92
75    <uses-feature
75-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:60:5-89
76        android:name="android.hardware.microphone"
76-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:60:19-61
77        android:required="false" />
77-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:60:62-86
78
79    <!-- AD TRACKING -->
80    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
80-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:63:5-79
80-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:63:22-76
81    <uses-permission
81-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:72:5-75
82        android:name="android.permission.READ_PHONE_STATE"
82-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:72:22-72
83        android:maxSdkVersion="29" />
83-->[:react-native-callkeep] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-callkeep\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:9-35
84
85    <queries>
85-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-11:15
86        <intent>
86-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-10:18
87            <action android:name="android.media.action.IMAGE_CAPTURE" />
87-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-73
87-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:21-70
88        </intent>
89        <intent>
89-->[com.pubscale.sdkone:offerwall:1.0.11] F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:17:9-19:18
90            <action android:name="android.intent.action.MAIN" />
90-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:126:17-69
90-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:126:25-66
91        </intent>
92        <intent>
92-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:11:9-17:18
93            <action android:name="android.intent.action.VIEW" />
93-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
93-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
94
95            <data
95-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
96                android:mimeType="*/*"
97                android:scheme="*" />
97-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
98        </intent>
99        <intent>
99-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:18:9-27:18
100            <action android:name="android.intent.action.VIEW" />
100-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
100-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
101
102            <category android:name="android.intent.category.BROWSABLE" />
102-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:17-78
102-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:27-75
103
104            <data
104-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
105                android:host="pay"
105-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:144:23-46
106                android:mimeType="*/*"
107                android:scheme="upi" />
107-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
108        </intent>
109        <intent>
109-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:31:9-35:18
110            <action android:name="android.intent.action.SEND" />
110-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:32:13-65
110-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:32:21-62
111
112            <data android:mimeType="*/*" />
112-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
113        </intent>
114        <intent>
114-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:36:9-38:18
115            <action android:name="rzp.device_token.share" />
115-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:37:13-61
115-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:37:21-58
116        </intent> <!-- For browser content -->
117        <intent>
117-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:38:9-44:18
118            <action android:name="android.intent.action.VIEW" />
118-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
118-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
119
120            <category android:name="android.intent.category.BROWSABLE" />
120-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:17-78
120-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:27-75
121
122            <data android:scheme="https" />
122-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
122-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
123        </intent> <!-- End of browser content -->
124        <!-- For CustomTabsService -->
125        <intent>
125-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:47:9-49:18
126            <action android:name="android.support.customtabs.action.CustomTabsService" />
126-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:48:13-90
126-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:48:21-87
127        </intent> <!-- End of CustomTabsService -->
128        <!-- For MRAID capabilities -->
129        <intent>
129-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:52:9-56:18
130            <action android:name="android.intent.action.INSERT" />
130-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:53:13-67
130-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:53:21-64
131
132            <data android:mimeType="vnd.android.cursor.dir/event" />
132-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
133        </intent>
134        <intent>
134-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:57:9-61:18
135            <action android:name="android.intent.action.VIEW" />
135-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
135-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
136
137            <data android:scheme="sms" />
137-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
137-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
138        </intent>
139        <intent>
139-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:62:9-66:18
140            <action android:name="android.intent.action.DIAL" />
140-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:63:13-65
140-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:63:21-62
141
142            <data android:path="tel:" />
142-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
143        </intent>
144        <intent>
144-->[androidx.camera:camera-extensions:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:23:9-25:18
145            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
145-->[androidx.camera:camera-extensions:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:24:13-86
145-->[androidx.camera:camera-extensions:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:24:21-83
146        </intent>
147    </queries>
148
149    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
149-->[com.pubscale.sdkone:offerwall:1.0.11] F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:11:5-98
149-->[com.pubscale.sdkone:offerwall:1.0.11] F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:11:22-95
150    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
150-->[com.pubscale.sdkone:offerwall:1.0.11] F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:12:5-14:47
150-->[com.pubscale.sdkone:offerwall:1.0.11] F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:13:9-62
151    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
151-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
151-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
152    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
152-->[com.google.android.gms:play-services-measurement-api:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
152-->[com.google.android.gms:play-services-measurement-api:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
153    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
153-->[com.google.android.gms:play-services-measurement-api:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
153-->[com.google.android.gms:play-services-measurement-api:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
154    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
154-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:29:5-83
154-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:29:22-80
155    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
155-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
155-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
156
157    <uses-feature
157-->[com.google.android.gms:play-services-maps:17.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:26:5-28:35
158        android:glEsVersion="0x00020000"
158-->[com.google.android.gms:play-services-maps:17.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:27:9-41
159        android:required="true" />
159-->[com.google.android.gms:play-services-maps:17.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:28:9-32
160
161    <permission
161-->[androidx.core:core:1.16.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
162        android:name="com.adtip.app.adtip_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
162-->[androidx.core:core:1.16.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
163        android:protectionLevel="signature" />
163-->[androidx.core:core:1.16.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
164
165    <uses-permission android:name="com.adtip.app.adtip_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
165-->[androidx.core:core:1.16.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
165-->[androidx.core:core:1.16.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
166    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
166-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:16:5-79
166-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:16:22-76
167    <uses-permission
167-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:17:5-19:38
168        android:name="android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS"
168-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:18:9-73
169        android:maxSdkVersion="30" /> <!-- For Xiaomi devices to enable heads-up notifications as default (https://github.com/invertase/notifee/issues/296) -->
169-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:19:9-35
170    <uses-permission
170-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:21:5-23:38
171        android:name="android.permission.ACCESS_NOTIFICATION_POLICY"
171-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:22:9-69
172        android:minSdkVersion="23" />
172-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:23:9-35
173
174    <application
174-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:77:5-214:19
175        android:name="com.adtip.app.adtip_app.MainApplication"
175-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:78:9-40
176        android:allowBackup="false"
176-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:82:9-36
177        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
177-->[androidx.core:core:1.16.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
178        android:extractNativeLibs="false"
179        android:icon="@mipmap/ic_launcher"
179-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:80:9-43
180        android:label="@string/app_name"
180-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:79:9-41
181        android:roundIcon="@mipmap/ic_launcher_round"
181-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:81:9-54
182        android:supportsRtl="true"
182-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:84:9-35
183        android:theme="@style/AppTheme"
183-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:83:9-40
184        android:usesCleartextTraffic="true" >
184-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:85:9-44
185        <meta-data
185-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:88:9-90:36
186            android:name="android.app.extract_native_libs"
186-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:89:13-59
187            android:value="true" />
187-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:90:13-33
188
189        <!-- AdMob App ID - PubScale Only -->
190        <!-- Note: Simplified ad system uses only PubScale ad units -->
191        <meta-data
191-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:94:9-97:45
192            android:name="com.google.android.gms.ads.APPLICATION_ID"
192-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:95:13-69
193            android:value="ca-app-pub-3206456546664189~6654042212" />
193-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:96:13-67
194
195        <!-- VIDEOSDK FOREGROUND SERVICE CONFIGURATION -->
196        <meta-data
196-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:100:9-102:52
197            android:name="live.videosdk.rnfgservice.notification_channel_name"
197-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:101:13-79
198            android:value="Meeting Notification" />
198-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:102:13-49
199        <meta-data
199-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:103:9-105:82
200            android:name="live.videosdk.rnfgservice.notification_channel_description"
200-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:104:13-86
201            android:value="Whenever meeting started notification will appear." />
201-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:105:13-79
202        <meta-data
202-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:106:9-108:63
203            android:name="live.videosdk.rnfgservice.notification_color"
203-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:107:13-72
204            android:resource="@android:color/holo_red_dark" />
204-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:108:13-60
205
206        <!-- Firebase messaging default notification channel -->
207        <meta-data
207-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:111:9-114:45
208            android:name="com.google.firebase.messaging.default_notification_channel_id"
208-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:112:13-89
209            android:value="adtip_call_channel" />
209-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:113:13-47
210
211        <activity
211-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:116:9-173:20
212            android:name="com.adtip.app.adtip_app.MainActivity"
212-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:117:13-41
213            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
213-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:119:13-122
214            android:exported="true"
214-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:122:13-36
215            android:label="@string/app_name"
215-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:118:13-45
216            android:launchMode="singleTask"
216-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:120:13-44
217            android:showWhenLocked="true"
217-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:123:13-42
218            android:turnScreenOn="true"
218-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:124:13-40
219            android:windowSoftInputMode="adjustPan" >
219-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:121:13-52
220            <intent-filter>
220-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:125:13-128:29
221                <action android:name="android.intent.action.MAIN" />
221-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:126:17-69
221-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:126:25-66
222
223                <category android:name="android.intent.category.LAUNCHER" />
223-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:127:17-77
223-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:127:27-74
224            </intent-filter>
225
226            <!-- Custom URL Scheme Deep Links -->
227            <intent-filter>
227-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:131:13-136:29
228                <action android:name="android.intent.action.VIEW" />
228-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
228-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
229
230                <category android:name="android.intent.category.DEFAULT" />
230-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:17-76
230-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:27-73
231                <category android:name="android.intent.category.BROWSABLE" />
231-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:17-78
231-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:27-75
232
233                <data android:scheme="adtip" />
233-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
233-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
234            </intent-filter>
235
236            <!-- Universal Links / App Links for adtip.in -->
237            <intent-filter android:autoVerify="true" >
237-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:139:13-145:29
237-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:139:28-53
238                <action android:name="android.intent.action.VIEW" />
238-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
238-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
239
240                <category android:name="android.intent.category.DEFAULT" />
240-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:17-76
240-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:27-73
241                <category android:name="android.intent.category.BROWSABLE" />
241-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:17-78
241-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:27-75
242
243                <data
243-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
244                    android:host="adtip.in"
244-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:144:23-46
245                    android:scheme="https" />
245-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
246            </intent-filter>
247
248            <!-- Universal Links for www.adtip.in -->
249            <intent-filter android:autoVerify="true" >
249-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:148:13-154:29
249-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:148:28-53
250                <action android:name="android.intent.action.VIEW" />
250-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
250-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
251
252                <category android:name="android.intent.category.DEFAULT" />
252-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:17-76
252-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:27-73
253                <category android:name="android.intent.category.BROWSABLE" />
253-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:17-78
253-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:27-75
254
255                <data
255-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
256                    android:host="www.adtip.in"
256-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:144:23-46
257                    android:scheme="https" />
257-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
258            </intent-filter>
259
260            <!-- Universal Links for app.adtip.in -->
261            <intent-filter android:autoVerify="true" >
261-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:157:13-163:29
261-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:157:28-53
262                <action android:name="android.intent.action.VIEW" />
262-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
262-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
263
264                <category android:name="android.intent.category.DEFAULT" />
264-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:17-76
264-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:27-73
265                <category android:name="android.intent.category.BROWSABLE" />
265-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:17-78
265-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:27-75
266
267                <data
267-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
268                    android:host="app.adtip.in"
268-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:144:23-46
269                    android:scheme="https" />
269-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
270            </intent-filter>
271            <intent-filter android:autoVerify="true" >
271-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:165:13-171:29
271-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:165:28-53
272                <action android:name="android.intent.action.VIEW" />
272-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
272-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
273
274                <category android:name="android.intent.category.DEFAULT" />
274-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:17-76
274-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:27-73
275                <category android:name="android.intent.category.BROWSABLE" />
275-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:17-78
275-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:27-75
276
277                <data android:scheme="https" />
277-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
277-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
278                <data android:host="adtip.com" />
278-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
278-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:144:23-46
279            </intent-filter>
280        </activity>
281
282        <!-- VIDEOSDK FOREGROUND SERVICE -->
283        <service
283-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:176:9-179:40
284            android:name="live.videosdk.rnfgservice.ForegroundService"
284-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:177:13-71
285            android:exported="false"
285-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:179:13-37
286            android:foregroundServiceType="camera|microphone" />
286-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:178:13-62
287
288        <!-- Notifee's foreground service declaration for camera and microphone -->
289        <service
289-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:182:9-155
290            android:name="app.notifee.core.ForegroundService"
290-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:182:18-67
291            android:exported="false"
291-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:182:128-152
292            android:foregroundServiceType="camera|microphone|phoneCall|shortService" />
292-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:182:68-127
293
294        <!-- CALLLEEP CONNECTION SERVICE -->
295        <service
295-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:185:9-197:19
296            android:name="io.wazo.callkeep.VoiceConnectionService"
296-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:186:13-67
297            android:exported="true"
297-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:190:13-36
298            android:foregroundServiceType="phoneCall|camera|microphone"
298-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:189:13-72
299            android:label="VoiceConnectionService"
299-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:187:13-51
300            android:permission="android.permission.BIND_TELECOM_CONNECTION_SERVICE" >
300-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:188:13-84
301            <intent-filter>
301-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:191:13-193:29
302                <action android:name="android.telecom.ConnectionService" />
302-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:192:17-76
302-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:192:25-73
303            </intent-filter>
304
305            <meta-data
305-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:194:13-196:69
306                android:name="android.telecom.CONNECTION_SERVICE"
306-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:195:17-66
307                android:value="android.telecom.ConnectionService" />
307-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:196:17-66
308        </service>
309        <service android:name="io.wazo.callkeep.RNCallKeepBackgroundMessagingService" />
309-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:198:9-89
309-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:198:18-86
310
311        <!-- ADTIP FIREBASE MESSAGING SERVICE -->
312        <service
312-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:201:9-207:19
313            android:name="com.adtip.app.adtip_app.AdtipFirebaseMessagingService"
313-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:202:13-81
314            android:exported="false" >
314-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:203:13-37
315            <intent-filter>
315-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:204:13-206:29
316                <action android:name="com.google.firebase.MESSAGING_EVENT" />
316-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:205:17-78
316-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:205:25-75
317            </intent-filter>
318        </service>
319
320        <!-- ADTIP CALL RINGING SERVICE -->
321        <service
321-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:210:9-212:40
322            android:name="com.adtip.app.adtip_app.CallRingingService"
322-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:211:13-70
323            android:exported="false" />
323-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:212:13-37
324
325        <meta-data
325-->[:react-native-google-mobile-ads] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-17:37
326            android:name="com.google.android.gms.ads.DELAY_APP_MEASUREMENT_INIT"
326-->[:react-native-google-mobile-ads] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-81
327            android:value="false" />
327-->[:react-native-google-mobile-ads] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-34
328        <meta-data
328-->[:react-native-google-mobile-ads] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:9-20:36
329            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
329-->[:react-native-google-mobile-ads] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:13-83
330            android:value="true" />
330-->[:react-native-google-mobile-ads] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-33
331        <meta-data
331-->[:react-native-google-mobile-ads] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:9-23:36
332            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
332-->[:react-native-google-mobile-ads] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-79
333            android:value="true" />
333-->[:react-native-google-mobile-ads] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-33
334
335        <provider
335-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-16:20
336            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
336-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-83
337            android:authorities="com.adtip.app.adtip_app.fileprovider"
337-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-64
338            android:exported="false"
338-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-37
339            android:grantUriPermissions="true" >
339-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-47
340            <meta-data
340-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
341                android:name="android.support.FILE_PROVIDER_PATHS"
341-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
342                android:resource="@xml/file_provider_paths" />
342-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
343        </provider>
344        <provider
344-->[:notifee_react-native] F:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:40
345            android:name="io.invertase.notifee.NotifeeInitProvider"
345-->[:notifee_react-native] F:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-68
346            android:authorities="com.adtip.app.adtip_app.notifee-init-provider"
346-->[:notifee_react-native] F:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-73
347            android:exported="false"
347-->[:notifee_react-native] F:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-37
348            android:initOrder="-100" />
348-->[:notifee_react-native] F:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
349
350        <meta-data
350-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-14:36
351            android:name="firebase_analytics_collection_enabled"
351-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-65
352            android:value="true" />
352-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-33
353        <meta-data
353-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-17:37
354            android:name="firebase_analytics_collection_deactivated"
354-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-69
355            android:value="false" />
355-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-34
356        <meta-data
356-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:9-20:36
357            android:name="google_analytics_adid_collection_enabled"
357-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:13-68
358            android:value="true" />
358-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-33
359        <meta-data
359-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:9-23:36
360            android:name="google_analytics_ssaid_collection_enabled"
360-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-69
361            android:value="true" />
361-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-33
362        <meta-data
362-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:9-26:36
363            android:name="google_analytics_automatic_screen_reporting_enabled"
363-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:13-79
364            android:value="true" />
364-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:13-33
365        <meta-data
365-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:9-29:36
366            android:name="google_analytics_default_allow_analytics_storage"
366-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-76
367            android:value="true" />
367-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-33
368        <meta-data
368-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:9-32:36
369            android:name="google_analytics_default_allow_ad_storage"
369-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:13-69
370            android:value="true" />
370-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:32:13-33
371        <meta-data
371-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:9-35:36
372            android:name="google_analytics_default_allow_ad_user_data"
372-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:13-71
373            android:value="true" />
373-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:35:13-33
374        <meta-data
374-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:9-38:36
375            android:name="google_analytics_default_allow_ad_personalization_signals"
375-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:13-85
376            android:value="true" /> <!-- Disable crashlytics by default so we can custom init with CrashlyticsNdk support -->
376-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:13-33
377        <meta-data
377-->[:react-native-firebase_crashlytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:9-12:37
378            android:name="firebase_crashlytics_collection_enabled"
378-->[:react-native-firebase_crashlytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-67
379            android:value="false" />
379-->[:react-native-firebase_crashlytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-34
380
381        <provider
381-->[:react-native-firebase_crashlytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:9-18:38
382            android:name="io.invertase.firebase.crashlytics.ReactNativeFirebaseCrashlyticsInitProvider"
382-->[:react-native-firebase_crashlytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-104
383            android:authorities="com.adtip.app.adtip_app.reactnativefirebasecrashlyticsinitprovider"
383-->[:react-native-firebase_crashlytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-94
384            android:exported="false"
384-->[:react-native-firebase_crashlytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-37
385            android:initOrder="98" />
385-->[:react-native-firebase_crashlytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-35
386
387        <service
387-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-14:40
388            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingHeadlessService"
388-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-103
389            android:exported="false" />
389-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-37
390        <service
390-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-21:19
391            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingService"
391-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-95
392            android:exported="false" >
392-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-37
393            <intent-filter>
393-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:204:13-206:29
394                <action android:name="com.google.firebase.MESSAGING_EVENT" />
394-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:205:17-78
394-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:205:25-75
395            </intent-filter>
396        </service>
397
398        <receiver
398-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:9-30:20
399            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingReceiver"
399-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-96
400            android:exported="true"
400-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:13-36
401            android:permission="com.google.android.c2dm.permission.SEND" >
401-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:13-73
402            <intent-filter>
402-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-29:29
403                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
403-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:17-81
403-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:25-78
404            </intent-filter>
405        </receiver>
406
407        <meta-data
407-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:32:9-34:37
408            android:name="delivery_metrics_exported_to_big_query_enabled"
408-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:13-74
409            android:value="false" />
409-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:13-34
410        <meta-data
410-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:35:9-37:36
411            android:name="firebase_messaging_auto_init_enabled"
411-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:13-64
412            android:value="true" />
412-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:13-33
413        <meta-data
413-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:9-40:37
414            android:name="firebase_messaging_notification_delegation_enabled"
414-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:39:13-78
415            android:value="false" />
415-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:40:13-34
416        <meta-data
416-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:9-46:47
417            android:name="com.google.firebase.messaging.default_notification_color"
417-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:45:13-84
418            android:resource="@color/white" />
418-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:46:13-44
419        <meta-data
419-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-11:36
420            android:name="app_data_collection_default_enabled"
420-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-63
421            android:value="true" />
421-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-33
422
423        <service
423-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:9-21:19
424            android:name="com.google.firebase.components.ComponentDiscoveryService"
424-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-84
425            android:directBootAware="true"
425-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-43
426            android:exported="false" >
426-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-37
427            <meta-data
427-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-20:85
428                android:name="com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar"
428-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:17-120
429                android:value="com.google.firebase.components.ComponentRegistrar" />
429-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:17-82
430            <meta-data
430-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
431                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
431-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
432                android:value="com.google.firebase.components.ComponentRegistrar" />
432-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
433            <meta-data
433-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
434                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
434-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
435                android:value="com.google.firebase.components.ComponentRegistrar" />
435-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
436            <meta-data
436-->[com.google.firebase:firebase-crashlytics-ndk:19.4.4] F:\R17DevTools\.gradle\caches\8.13\transforms\aeb8af0b6421a1d3b2ea7118ef2989f3\transformed\jetified-firebase-crashlytics-ndk-19.4.4\AndroidManifest.xml:32:13-34:85
437                android:name="com.google.firebase.components:com.google.firebase.crashlytics.ndk.CrashlyticsNdkRegistrar"
437-->[com.google.firebase:firebase-crashlytics-ndk:19.4.4] F:\R17DevTools\.gradle\caches\8.13\transforms\aeb8af0b6421a1d3b2ea7118ef2989f3\transformed\jetified-firebase-crashlytics-ndk-19.4.4\AndroidManifest.xml:33:17-122
438                android:value="com.google.firebase.components.ComponentRegistrar" />
438-->[com.google.firebase:firebase-crashlytics-ndk:19.4.4] F:\R17DevTools\.gradle\caches\8.13\transforms\aeb8af0b6421a1d3b2ea7118ef2989f3\transformed\jetified-firebase-crashlytics-ndk-19.4.4\AndroidManifest.xml:34:17-82
439            <meta-data
439-->[com.google.firebase:firebase-crashlytics:19.4.4] F:\R17DevTools\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:15:13-17:85
440                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
440-->[com.google.firebase:firebase-crashlytics:19.4.4] F:\R17DevTools\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:16:17-126
441                android:value="com.google.firebase.components.ComponentRegistrar" />
441-->[com.google.firebase:firebase-crashlytics:19.4.4] F:\R17DevTools\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:17:17-82
442            <meta-data
442-->[com.google.firebase:firebase-crashlytics:19.4.4] F:\R17DevTools\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:18:13-20:85
443                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
443-->[com.google.firebase:firebase-crashlytics:19.4.4] F:\R17DevTools\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:19:17-115
444                android:value="com.google.firebase.components.ComponentRegistrar" />
444-->[com.google.firebase:firebase-crashlytics:19.4.4] F:\R17DevTools\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:20:17-82
445            <meta-data
445-->[com.google.android.gms:play-services-measurement-api:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
446                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
446-->[com.google.android.gms:play-services-measurement-api:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
447                android:value="com.google.firebase.components.ComponentRegistrar" />
447-->[com.google.android.gms:play-services-measurement-api:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
448            <meta-data
448-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
449                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
449-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
450                android:value="com.google.firebase.components.ComponentRegistrar" />
450-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
451            <meta-data
451-->[com.google.firebase:firebase-database:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
452                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
452-->[com.google.firebase:firebase-database:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
453                android:value="com.google.firebase.components.ComponentRegistrar" />
453-->[com.google.firebase:firebase-database:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
454            <meta-data
454-->[com.google.firebase:firebase-database:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
455                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
455-->[com.google.firebase:firebase-database:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
456                android:value="com.google.firebase.components.ComponentRegistrar" />
456-->[com.google.firebase:firebase-database:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
457            <meta-data
457-->[com.google.firebase:firebase-firestore:25.1.4] F:\R17DevTools\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
458                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
458-->[com.google.firebase:firebase-firestore:25.1.4] F:\R17DevTools\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
459                android:value="com.google.firebase.components.ComponentRegistrar" />
459-->[com.google.firebase:firebase-firestore:25.1.4] F:\R17DevTools\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
460            <meta-data
460-->[com.google.firebase:firebase-firestore:25.1.4] F:\R17DevTools\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
461                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
461-->[com.google.firebase:firebase-firestore:25.1.4] F:\R17DevTools\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
462                android:value="com.google.firebase.components.ComponentRegistrar" />
462-->[com.google.firebase:firebase-firestore:25.1.4] F:\R17DevTools\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
463            <meta-data
463-->[com.google.firebase:firebase-storage:21.0.2] F:\R17DevTools\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:30:13-32:85
464                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
464-->[com.google.firebase:firebase-storage:21.0.2] F:\R17DevTools\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:31:17-118
465                android:value="com.google.firebase.components.ComponentRegistrar" />
465-->[com.google.firebase:firebase-storage:21.0.2] F:\R17DevTools\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:32:17-82
466            <meta-data
466-->[com.google.firebase:firebase-storage:21.0.2] F:\R17DevTools\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:33:13-35:85
467                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
467-->[com.google.firebase:firebase-storage:21.0.2] F:\R17DevTools\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:34:17-107
468                android:value="com.google.firebase.components.ComponentRegistrar" />
468-->[com.google.firebase:firebase-storage:21.0.2] F:\R17DevTools\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:35:17-82
469            <meta-data
469-->[com.google.firebase:firebase-sessions:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:29:13-31:85
470                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
470-->[com.google.firebase:firebase-sessions:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:30:17-117
471                android:value="com.google.firebase.components.ComponentRegistrar" />
471-->[com.google.firebase:firebase-sessions:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:31:17-82
472            <meta-data
472-->[com.google.firebase:firebase-installations:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
473                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
473-->[com.google.firebase:firebase-installations:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
474                android:value="com.google.firebase.components.ComponentRegistrar" />
474-->[com.google.firebase:firebase-installations:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
475            <meta-data
475-->[com.google.firebase:firebase-installations:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
476                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
476-->[com.google.firebase:firebase-installations:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
477                android:value="com.google.firebase.components.ComponentRegistrar" />
477-->[com.google.firebase:firebase-installations:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
478            <meta-data
478-->[com.google.firebase:firebase-appcheck:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
479                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
479-->[com.google.firebase:firebase-appcheck:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
480                android:value="com.google.firebase.components.ComponentRegistrar" />
480-->[com.google.firebase:firebase-appcheck:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
481            <meta-data
481-->[com.google.firebase:firebase-appcheck:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
482                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
482-->[com.google.firebase:firebase-appcheck:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
483                android:value="com.google.firebase.components.ComponentRegistrar" />
483-->[com.google.firebase:firebase-appcheck:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
484            <meta-data
484-->[com.google.firebase:firebase-common-ktx:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\7154a9c5f3c74624ae64a9d79896bb9e\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
485                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
485-->[com.google.firebase:firebase-common-ktx:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\7154a9c5f3c74624ae64a9d79896bb9e\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
486                android:value="com.google.firebase.components.ComponentRegistrar" />
486-->[com.google.firebase:firebase-common-ktx:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\7154a9c5f3c74624ae64a9d79896bb9e\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
487            <meta-data
487-->[com.google.firebase:firebase-datatransport:19.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\8bcdce69c2e38ab05a17e87b8e240c89\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
488                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
488-->[com.google.firebase:firebase-datatransport:19.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\8bcdce69c2e38ab05a17e87b8e240c89\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
489                android:value="com.google.firebase.components.ComponentRegistrar" />
489-->[com.google.firebase:firebase-datatransport:19.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\8bcdce69c2e38ab05a17e87b8e240c89\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
490            <meta-data
490-->[com.google.firebase:firebase-common:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
491                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
491-->[com.google.firebase:firebase-common:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
492                android:value="com.google.firebase.components.ComponentRegistrar" />
492-->[com.google.firebase:firebase-common:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
493        </service>
494
495        <provider
495-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:9-27:38
496            android:name="io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider"
496-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-88
497            android:authorities="com.adtip.app.adtip_app.reactnativefirebaseappinitprovider"
497-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:13-86
498            android:exported="false"
498-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:13-37
499            android:initOrder="99" />
499-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-35
500        <provider
500-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:9-26:20
501            android:name="com.reactnative.ivpusic.imagepicker.IvpusicImagePickerFileProvider"
501-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:13-94
502            android:authorities="com.adtip.app.adtip_app.provider"
502-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-60
503            android:exported="false"
503-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-37
504            android:grantUriPermissions="true" >
504-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-47
505            <meta-data
505-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
506                android:name="android.support.FILE_PROVIDER_PATHS"
506-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
507                android:resource="@xml/ivpusic_imagepicker_provider_paths" />
507-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
508        </provider>
509
510        <activity
510-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:9-30:72
511            android:name="com.yalantis.ucrop.UCropActivity"
511-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-60
512            android:theme="@style/Theme.AppCompat.Light.NoActionBar" /> <!-- Prompt Google Play services to install the backported photo picker module -->
512-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-69
513        <service
513-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:9-44:19
514            android:name="com.google.android.gms.metadata.ModuleDependencies"
514-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:13-78
515            android:enabled="false"
515-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:35:13-36
516            android:exported="false" >
516-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:13-37
517            <intent-filter>
517-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:13-39:29
518                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
518-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:17-94
518-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:25-91
519            </intent-filter>
520
521            <meta-data
521-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:41:13-43:36
522                android:name="photopicker_activity:0:required"
522-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:17-63
523                android:value="" />
523-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:17-33
524        </service>
525
526        <provider
526-->[:react-native-image-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-16:20
527            android:name="com.imagepicker.ImagePickerProvider"
527-->[:react-native-image-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-63
528            android:authorities="com.adtip.app.adtip_app.imagepickerprovider"
528-->[:react-native-image-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-71
529            android:exported="false"
529-->[:react-native-image-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-37
530            android:grantUriPermissions="true" >
530-->[:react-native-image-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-47
531            <meta-data
531-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
532                android:name="android.support.FILE_PROVIDER_PATHS"
532-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
533                android:resource="@xml/imagepicker_provider_paths" />
533-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
534        </provider>
535
536        <activity
536-->[:react-native-razorpay] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-10:86
537            android:name="com.razorpay.CheckoutActivity"
537-->[:react-native-razorpay] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-57
538            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
538-->[:react-native-razorpay] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-83
539            android:exported="false"
539-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:45:13-37
540            android:theme="@style/CheckoutTheme" >
540-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:46:13-49
541            <intent-filter>
541-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:47:13-49:29
542                <action android:name="android.intent.action.MAIN" />
542-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:126:17-69
542-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:126:25-66
543            </intent-filter>
544        </activity>
545        <activity
545-->[com.pubscale.sdkone:offerwall:1.0.11] F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:23:9-27:65
546            android:name="com.pubscale.sdkone.offerwall.ui.OfferWallActivity"
546-->[com.pubscale.sdkone:offerwall:1.0.11] F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:24:13-78
547            android:configChanges="orientation|screenSize"
547-->[com.pubscale.sdkone:offerwall:1.0.11] F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:25:13-59
548            android:exported="false"
548-->[com.pubscale.sdkone:offerwall:1.0.11] F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:26:13-37
549            android:theme="@style/Theme.PubScaleOfferWallSDK" />
549-->[com.pubscale.sdkone:offerwall:1.0.11] F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:27:13-62
550
551        <receiver
551-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
552            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
552-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
553            android:exported="true"
553-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
554            android:permission="com.google.android.c2dm.permission.SEND" >
554-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
555            <intent-filter>
555-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-29:29
556                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
556-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:17-81
556-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:25-78
557            </intent-filter>
558
559            <meta-data
559-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
560                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
560-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
561                android:value="true" />
561-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
562        </receiver>
563        <!--
564             FirebaseMessagingService performs security checks at runtime,
565             but set to not exported to explicitly avoid allowing another app to call it.
566        -->
567        <service
567-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
568            android:name="com.google.firebase.messaging.FirebaseMessagingService"
568-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
569            android:directBootAware="true"
569-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
570            android:exported="false" >
570-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
571            <intent-filter android:priority="-500" >
571-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:204:13-206:29
572                <action android:name="com.google.firebase.MESSAGING_EVENT" />
572-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:205:17-78
572-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:205:25-75
573            </intent-filter>
574        </service>
575
576        <provider
576-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:52:9-60:20
577            android:name="androidx.startup.InitializationProvider"
577-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:53:13-67
578            android:authorities="com.adtip.app.adtip_app.androidx-startup"
578-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:54:13-68
579            android:exported="false" >
579-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:55:13-37
580            <meta-data
580-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:57:13-59:52
581                android:name="com.razorpay.RazorpayInitializer"
581-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:58:17-64
582                android:value="androidx.startup" />
582-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:59:17-49
583            <meta-data
583-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
584                android:name="androidx.work.WorkManagerInitializer"
584-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
585                android:value="androidx.startup" />
585-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
586            <meta-data
586-->[androidx.emoji2:emoji2:1.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\c02c6a58638664cbfa4f398f77214d5c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
587                android:name="androidx.emoji2.text.EmojiCompatInitializer"
587-->[androidx.emoji2:emoji2:1.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\c02c6a58638664cbfa4f398f77214d5c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
588                android:value="androidx.startup" />
588-->[androidx.emoji2:emoji2:1.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\c02c6a58638664cbfa4f398f77214d5c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
589            <meta-data
589-->[androidx.lifecycle:lifecycle-process:2.8.7] F:\R17DevTools\.gradle\caches\8.13\transforms\88ef7ff10d1cfe00bba6c2d4e1214614\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
590                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
590-->[androidx.lifecycle:lifecycle-process:2.8.7] F:\R17DevTools\.gradle\caches\8.13\transforms\88ef7ff10d1cfe00bba6c2d4e1214614\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
591                android:value="androidx.startup" />
591-->[androidx.lifecycle:lifecycle-process:2.8.7] F:\R17DevTools\.gradle\caches\8.13\transforms\88ef7ff10d1cfe00bba6c2d4e1214614\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
592            <meta-data
592-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
593                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
593-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
594                android:value="androidx.startup" />
594-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
595        </provider>
596
597        <activity
597-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:62:9-65:75
598            android:name="com.razorpay.MagicXActivity"
598-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:63:13-55
599            android:exported="false"
599-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:64:13-37
600            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
600-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:65:13-72
601
602        <meta-data
602-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:67:9-69:58
603            android:name="com.razorpay.plugin.googlepay_all"
603-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:68:13-61
604            android:value="com.razorpay.RzpGpayMerged" />
604-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:69:13-55
605
606        <activity
606-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
607            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
607-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
608            android:excludeFromRecents="true"
608-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
609            android:exported="true"
609-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
610            android:launchMode="singleTask"
610-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
611            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
611-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
612            <intent-filter>
612-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
613                <action android:name="android.intent.action.VIEW" />
613-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
613-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
614
615                <category android:name="android.intent.category.DEFAULT" />
615-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:17-76
615-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:27-73
616                <category android:name="android.intent.category.BROWSABLE" />
616-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:17-78
616-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:27-75
617
618                <data
618-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
619                    android:host="firebase.auth"
619-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:144:23-46
620                    android:path="/"
621                    android:scheme="genericidp" />
621-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
622            </intent-filter>
623        </activity>
624        <activity
624-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
625            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
625-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
626            android:excludeFromRecents="true"
626-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
627            android:exported="true"
627-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
628            android:launchMode="singleTask"
628-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
629            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
629-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
630            <intent-filter>
630-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
631                <action android:name="android.intent.action.VIEW" />
631-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
631-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
632
633                <category android:name="android.intent.category.DEFAULT" />
633-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:17-76
633-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:27-73
634                <category android:name="android.intent.category.BROWSABLE" />
634-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:17-78
634-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:27-75
635
636                <data
636-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
637                    android:host="firebase.auth"
637-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:144:23-46
638                    android:path="/"
639                    android:scheme="recaptcha" />
639-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
640            </intent-filter>
641        </activity>
642
643        <service
643-->[com.google.firebase:firebase-sessions:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:22:9-25:40
644            android:name="com.google.firebase.sessions.SessionLifecycleService"
644-->[com.google.firebase:firebase-sessions:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:23:13-80
645            android:enabled="true"
645-->[com.google.firebase:firebase-sessions:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:24:13-35
646            android:exported="false" />
646-->[com.google.firebase:firebase-sessions:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:25:13-37
647
648        <meta-data
648-->[com.github.bumptech.glide:okhttp3-integration:4.14.2] F:\R17DevTools\.gradle\caches\8.13\transforms\819885695693bbcd94c9388920867bc1\transformed\jetified-okhttp3-integration-4.14.2\AndroidManifest.xml:10:9-12:43
649            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
649-->[com.github.bumptech.glide:okhttp3-integration:4.14.2] F:\R17DevTools\.gradle\caches\8.13\transforms\819885695693bbcd94c9388920867bc1\transformed\jetified-okhttp3-integration-4.14.2\AndroidManifest.xml:11:13-84
650            android:value="GlideModule" />
650-->[com.github.bumptech.glide:okhttp3-integration:4.14.2] F:\R17DevTools\.gradle\caches\8.13\transforms\819885695693bbcd94c9388920867bc1\transformed\jetified-okhttp3-integration-4.14.2\AndroidManifest.xml:12:13-40
651
652        <service
652-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] F:\R17DevTools\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:9:9-15:19
653            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
653-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] F:\R17DevTools\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:10:13-91
654            android:directBootAware="true"
654-->[com.google.mlkit:common:18.11.0] F:\R17DevTools\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:17:13-43
655            android:exported="false" >
655-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] F:\R17DevTools\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:11:13-37
656            <meta-data
656-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] F:\R17DevTools\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
657                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
657-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] F:\R17DevTools\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
658                android:value="com.google.firebase.components.ComponentRegistrar" />
658-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] F:\R17DevTools\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
659            <meta-data
659-->[com.google.mlkit:vision-common:17.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\8ac0cc6b1d80a9a5d39c72b858381152\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
660                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
660-->[com.google.mlkit:vision-common:17.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\8ac0cc6b1d80a9a5d39c72b858381152\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
661                android:value="com.google.firebase.components.ComponentRegistrar" />
661-->[com.google.mlkit:vision-common:17.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\8ac0cc6b1d80a9a5d39c72b858381152\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
662            <meta-data
662-->[com.google.mlkit:common:18.11.0] F:\R17DevTools\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:20:13-22:85
663                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
663-->[com.google.mlkit:common:18.11.0] F:\R17DevTools\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:21:17-120
664                android:value="com.google.firebase.components.ComponentRegistrar" />
664-->[com.google.mlkit:common:18.11.0] F:\R17DevTools\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:22:17-82
665        </service>
666
667        <provider
667-->[com.google.mlkit:common:18.11.0] F:\R17DevTools\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:9:9-13:38
668            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
668-->[com.google.mlkit:common:18.11.0] F:\R17DevTools\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:10:13-78
669            android:authorities="com.adtip.app.adtip_app.mlkitinitprovider"
669-->[com.google.mlkit:common:18.11.0] F:\R17DevTools\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:11:13-69
670            android:exported="false"
670-->[com.google.mlkit:common:18.11.0] F:\R17DevTools\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:12:13-37
671            android:initOrder="99" />
671-->[com.google.mlkit:common:18.11.0] F:\R17DevTools\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:13:13-35
672
673        <service
673-->[com.google.android.datatransport:transport-backend-cct:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
674            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
674-->[com.google.android.datatransport:transport-backend-cct:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
675            android:exported="false" >
675-->[com.google.android.datatransport:transport-backend-cct:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
676            <meta-data
676-->[com.google.android.datatransport:transport-backend-cct:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
677                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
677-->[com.google.android.datatransport:transport-backend-cct:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
678                android:value="cct" />
678-->[com.google.android.datatransport:transport-backend-cct:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
679        </service> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
680        <activity
680-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:73:9-78:43
681            android:name="com.google.android.gms.ads.AdActivity"
681-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:74:13-65
682            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
682-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:75:13-122
683            android:exported="false"
683-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:76:13-37
684            android:theme="@android:style/Theme.Translucent" />
684-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:77:13-61
685
686        <provider
686-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:80:9-85:43
687            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
687-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:81:13-76
688            android:authorities="com.adtip.app.adtip_app.mobileadsinitprovider"
688-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:82:13-73
689            android:exported="false"
689-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:83:13-37
690            android:initOrder="100" />
690-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:84:13-36
691
692        <service
692-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:87:9-91:43
693            android:name="com.google.android.gms.ads.AdService"
693-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:88:13-64
694            android:enabled="true"
694-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:89:13-35
695            android:exported="false" />
695-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:90:13-37
696
697        <activity
697-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:93:9-97:43
698            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
698-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:94:13-82
699            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
699-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:95:13-122
700            android:exported="false" />
700-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:96:13-37
701        <activity
701-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:98:9-105:43
702            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
702-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:99:13-82
703            android:excludeFromRecents="true"
703-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:100:13-46
704            android:exported="false"
704-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:101:13-37
705            android:launchMode="singleTask"
705-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:102:13-44
706            android:taskAffinity=""
706-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:103:13-36
707            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
707-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:104:13-72
708
709        <service
709-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
710            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
710-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
711            android:directBootAware="false"
711-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
712            android:enabled="@bool/enable_system_alarm_service_default"
712-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
713            android:exported="false" />
713-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
714        <service
714-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
715            android:name="androidx.work.impl.background.systemjob.SystemJobService"
715-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
716            android:directBootAware="false"
716-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
717            android:enabled="@bool/enable_system_job_service_default"
717-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
718            android:exported="true"
718-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
719            android:permission="android.permission.BIND_JOB_SERVICE" />
719-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
720        <service
720-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
721            android:name="androidx.work.impl.foreground.SystemForegroundService"
721-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
722            android:directBootAware="false"
722-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
723            android:enabled="@bool/enable_system_foreground_service_default"
723-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
724            android:exported="false" />
724-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
725
726        <receiver
726-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
727            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
727-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
728            android:directBootAware="false"
728-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
729            android:enabled="true"
729-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
730            android:exported="false" />
730-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
731        <receiver
731-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
732            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
732-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
733            android:directBootAware="false"
733-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
734            android:enabled="false"
734-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
735            android:exported="false" >
735-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
736            <intent-filter>
736-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
737                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
737-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
737-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
738                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
738-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
738-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
739            </intent-filter>
740        </receiver>
741        <receiver
741-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
742            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
742-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
743            android:directBootAware="false"
743-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
744            android:enabled="false"
744-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
745            android:exported="false" >
745-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
746            <intent-filter>
746-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
747                <action android:name="android.intent.action.BATTERY_OKAY" />
747-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
747-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
748                <action android:name="android.intent.action.BATTERY_LOW" />
748-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
748-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
749            </intent-filter>
750        </receiver>
751        <receiver
751-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
752            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
752-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
753            android:directBootAware="false"
753-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
754            android:enabled="false"
754-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
755            android:exported="false" >
755-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
756            <intent-filter>
756-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
757                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
757-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
757-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
758                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
758-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
758-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
759            </intent-filter>
760        </receiver>
761        <receiver
761-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
762            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
762-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
763            android:directBootAware="false"
763-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
764            android:enabled="false"
764-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
765            android:exported="false" >
765-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
766            <intent-filter>
766-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
767                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
767-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
767-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
768            </intent-filter>
769        </receiver>
770        <receiver
770-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
771            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
771-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
772            android:directBootAware="false"
772-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
773            android:enabled="false"
773-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
774            android:exported="false" >
774-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
775            <intent-filter>
775-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
776                <action android:name="android.intent.action.BOOT_COMPLETED" />
776-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
776-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
777                <action android:name="android.intent.action.TIME_SET" />
777-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
777-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
778                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
778-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
778-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
779            </intent-filter>
780        </receiver>
781        <receiver
781-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
782            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
782-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
783            android:directBootAware="false"
783-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
784            android:enabled="@bool/enable_system_alarm_service_default"
784-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
785            android:exported="false" >
785-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
786            <intent-filter>
786-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
787                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
787-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
787-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
788            </intent-filter>
789        </receiver>
790        <receiver
790-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
791            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
791-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
792            android:directBootAware="false"
792-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
793            android:enabled="true"
793-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
794            android:exported="true"
794-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
795            android:permission="android.permission.DUMP" >
795-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
796            <intent-filter>
796-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
797                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
797-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
797-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
798            </intent-filter>
799        </receiver>
800
801        <service
801-->[androidx.room:room-runtime:2.5.2] F:\R17DevTools\.gradle\caches\8.13\transforms\5e2acb9bee0a5b872b1d515edf8b6f46\transformed\room-runtime-2.5.2\AndroidManifest.xml:24:9-28:63
802            android:name="androidx.room.MultiInstanceInvalidationService"
802-->[androidx.room:room-runtime:2.5.2] F:\R17DevTools\.gradle\caches\8.13\transforms\5e2acb9bee0a5b872b1d515edf8b6f46\transformed\room-runtime-2.5.2\AndroidManifest.xml:25:13-74
803            android:directBootAware="true"
803-->[androidx.room:room-runtime:2.5.2] F:\R17DevTools\.gradle\caches\8.13\transforms\5e2acb9bee0a5b872b1d515edf8b6f46\transformed\room-runtime-2.5.2\AndroidManifest.xml:26:13-43
804            android:exported="false" />
804-->[androidx.room:room-runtime:2.5.2] F:\R17DevTools\.gradle\caches\8.13\transforms\5e2acb9bee0a5b872b1d515edf8b6f46\transformed\room-runtime-2.5.2\AndroidManifest.xml:27:13-37
805        <service
805-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
806            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
806-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
807            android:enabled="true"
807-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
808            android:exported="false" >
808-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
809            <meta-data
809-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
810                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
810-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
811                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
811-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
812        </service>
813
814        <activity
814-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
815            android:name="androidx.credentials.playservices.HiddenActivity"
815-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
816            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
816-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
817            android:enabled="true"
817-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
818            android:exported="false"
818-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
819            android:fitsSystemWindows="true"
819-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
820            android:theme="@style/Theme.Hidden" >
820-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
821        </activity>
822
823        <uses-library
823-->[androidx.camera:camera-extensions:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:29:9-31:40
824            android:name="androidx.camera.extensions.impl"
824-->[androidx.camera:camera-extensions:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:30:13-59
825            android:required="false" />
825-->[androidx.camera:camera-extensions:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:31:13-37
826
827        <service
827-->[androidx.camera:camera-camera2:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:24:9-33:19
828            android:name="androidx.camera.core.impl.MetadataHolderService"
828-->[androidx.camera:camera-camera2:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:25:13-75
829            android:enabled="false"
829-->[androidx.camera:camera-camera2:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:26:13-36
830            android:exported="false" >
830-->[androidx.camera:camera-camera2:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:27:13-37
831            <meta-data
831-->[androidx.camera:camera-camera2:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:30:13-32:89
832                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
832-->[androidx.camera:camera-camera2:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:31:17-103
833                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
833-->[androidx.camera:camera-camera2:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:32:17-86
834        </service>
835
836        <activity
836-->[com.google.android.gms:play-services-auth:21.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
837            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
837-->[com.google.android.gms:play-services-auth:21.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
838            android:excludeFromRecents="true"
838-->[com.google.android.gms:play-services-auth:21.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
839            android:exported="false"
839-->[com.google.android.gms:play-services-auth:21.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
840            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
840-->[com.google.android.gms:play-services-auth:21.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
841        <!--
842            Service handling Google Sign-In user revocation. For apps that do not integrate with
843            Google Sign-In, this service will never be started.
844        -->
845        <service
845-->[com.google.android.gms:play-services-auth:21.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
846            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
846-->[com.google.android.gms:play-services-auth:21.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
847            android:exported="true"
847-->[com.google.android.gms:play-services-auth:21.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
848            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
848-->[com.google.android.gms:play-services-auth:21.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
849            android:visibleToInstantApps="true" />
849-->[com.google.android.gms:play-services-auth:21.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
850
851        <meta-data
851-->[com.github.zjupure:webpdecoder:2.6.4.14.2] F:\R17DevTools\.gradle\caches\8.13\transforms\28d9696d1ba03482c83101bc9186888b\transformed\jetified-webpdecoder-2.6.4.14.2\AndroidManifest.xml:10:9-12:43
852            android:name="com.bumptech.glide.integration.webp.WebpGlideModule"
852-->[com.github.zjupure:webpdecoder:2.6.4.14.2] F:\R17DevTools\.gradle\caches\8.13\transforms\28d9696d1ba03482c83101bc9186888b\transformed\jetified-webpdecoder-2.6.4.14.2\AndroidManifest.xml:11:13-79
853            android:value="GlideModule" />
853-->[com.github.zjupure:webpdecoder:2.6.4.14.2] F:\R17DevTools\.gradle\caches\8.13\transforms\28d9696d1ba03482c83101bc9186888b\transformed\jetified-webpdecoder-2.6.4.14.2\AndroidManifest.xml:12:13-40
854
855        <receiver
855-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
856            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
856-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
857            android:enabled="true"
857-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
858            android:exported="false" >
858-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
859        </receiver>
860
861        <service
861-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
862            android:name="com.google.android.gms.measurement.AppMeasurementService"
862-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
863            android:enabled="true"
863-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
864            android:exported="false" />
864-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
865        <service
865-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
866            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
866-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
867            android:enabled="true"
867-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
868            android:exported="false"
868-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
869            android:permission="android.permission.BIND_JOB_SERVICE" /> <!-- Needs to be explicitly declared on P+ -->
869-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
870        <uses-library
870-->[com.google.android.gms:play-services-maps:17.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:33:9-35:40
871            android:name="org.apache.http.legacy"
871-->[com.google.android.gms:play-services-maps:17.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:34:13-50
872            android:required="false" />
872-->[com.google.android.gms:play-services-maps:17.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:35:13-37
873
874        <activity
874-->[com.google.android.gms:play-services-base:18.5.0] F:\R17DevTools\.gradle\caches\8.13\transforms\31cc88bd1a811e01477d23dfca8d3955\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
875            android:name="com.google.android.gms.common.api.GoogleApiActivity"
875-->[com.google.android.gms:play-services-base:18.5.0] F:\R17DevTools\.gradle\caches\8.13\transforms\31cc88bd1a811e01477d23dfca8d3955\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
876            android:exported="false"
876-->[com.google.android.gms:play-services-base:18.5.0] F:\R17DevTools\.gradle\caches\8.13\transforms\31cc88bd1a811e01477d23dfca8d3955\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
877            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
877-->[com.google.android.gms:play-services-base:18.5.0] F:\R17DevTools\.gradle\caches\8.13\transforms\31cc88bd1a811e01477d23dfca8d3955\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
878
879        <provider
879-->[com.google.firebase:firebase-common:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
880            android:name="com.google.firebase.provider.FirebaseInitProvider"
880-->[com.google.firebase:firebase-common:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
881            android:authorities="com.adtip.app.adtip_app.firebaseinitprovider"
881-->[com.google.firebase:firebase-common:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
882            android:directBootAware="true"
882-->[com.google.firebase:firebase-common:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
883            android:exported="false"
883-->[com.google.firebase:firebase-common:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
884            android:initOrder="100" />
884-->[com.google.firebase:firebase-common:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
885
886        <uses-library
886-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] F:\R17DevTools\.gradle\caches\8.13\transforms\32c6230c0475f108e440ef839e9bcc70\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
887            android:name="android.ext.adservices"
887-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] F:\R17DevTools\.gradle\caches\8.13\transforms\32c6230c0475f108e440ef839e9bcc70\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
888            android:required="false" />
888-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] F:\R17DevTools\.gradle\caches\8.13\transforms\32c6230c0475f108e440ef839e9bcc70\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
889
890        <meta-data
890-->[com.google.android.gms:play-services-basement:18.5.0] F:\R17DevTools\.gradle\caches\8.13\transforms\6ee598c7bab1dd2859d5d01f2229eaf5\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
891            android:name="com.google.android.gms.version"
891-->[com.google.android.gms:play-services-basement:18.5.0] F:\R17DevTools\.gradle\caches\8.13\transforms\6ee598c7bab1dd2859d5d01f2229eaf5\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
892            android:value="@integer/google_play_services_version" />
892-->[com.google.android.gms:play-services-basement:18.5.0] F:\R17DevTools\.gradle\caches\8.13\transforms\6ee598c7bab1dd2859d5d01f2229eaf5\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
893
894        <receiver
894-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
895            android:name="androidx.profileinstaller.ProfileInstallReceiver"
895-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
896            android:directBootAware="false"
896-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
897            android:enabled="true"
897-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
898            android:exported="true"
898-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
899            android:permission="android.permission.DUMP" >
899-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
900            <intent-filter>
900-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
901                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
901-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
901-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
902            </intent-filter>
903            <intent-filter>
903-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
904                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
904-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
904-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
905            </intent-filter>
906            <intent-filter>
906-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
907                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
907-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
907-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
908            </intent-filter>
909            <intent-filter>
909-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
910                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
910-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
910-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
911            </intent-filter>
912        </receiver>
913
914        <service
914-->[com.google.android.datatransport:transport-runtime:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
915            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
915-->[com.google.android.datatransport:transport-runtime:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
916            android:exported="false"
916-->[com.google.android.datatransport:transport-runtime:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
917            android:permission="android.permission.BIND_JOB_SERVICE" >
917-->[com.google.android.datatransport:transport-runtime:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
918        </service>
919
920        <receiver
920-->[com.google.android.datatransport:transport-runtime:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
921            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
921-->[com.google.android.datatransport:transport-runtime:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
922            android:exported="false" /> <!-- Receiver Service -->
922-->[com.google.android.datatransport:transport-runtime:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
923        <service
923-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:28:9-30:40
924            android:name="app.notifee.core.ReceiverService"
924-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:29:13-60
925            android:exported="false" />
925-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:30:13-37
926
927        <activity
927-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:32:9-38:75
928            android:name="app.notifee.core.NotificationReceiverActivity"
928-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:33:13-73
929            android:excludeFromRecents="true"
929-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:34:13-46
930            android:exported="true"
930-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:35:13-36
931            android:noHistory="true"
931-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:36:13-37
932            android:taskAffinity=""
932-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:37:13-36
933            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
933-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:38:13-72
934
935        <receiver
935-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:46:9-54:20
936            android:name="app.notifee.core.RebootBroadcastReceiver"
936-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:47:13-68
937            android:exported="false" >
937-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:48:13-37
938            <intent-filter>
938-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:49:13-53:29
939                <action android:name="android.intent.action.BOOT_COMPLETED" />
939-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
939-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
940                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
940-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:51:17-82
940-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:51:25-79
941                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
941-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:52:17-82
941-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:52:25-79
942            </intent-filter>
943        </receiver>
944        <receiver
944-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:55:9-61:20
945            android:name="app.notifee.core.AlarmPermissionBroadcastReceiver"
945-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:56:13-77
946            android:exported="true" >
946-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:57:13-36
947            <intent-filter>
947-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:58:13-60:29
948                <action android:name="android.app.action.SCHEDULE_EXACT_ALARM_PERMISSION_STATE_CHANGED" />
948-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:59:17-107
948-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:59:25-104
949            </intent-filter>
950        </receiver>
951        <receiver
951-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:62:9-70:20
952            android:name="app.notifee.core.NotificationAlarmReceiver"
952-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:63:13-70
953            android:exported="false" >
953-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:64:13-37
954            <intent-filter>
954-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:49:13-53:29
955                <action android:name="android.intent.action.BOOT_COMPLETED" />
955-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
955-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
956                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
956-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:51:17-82
956-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:51:25-79
957                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
957-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:52:17-82
957-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:52:25-79
958            </intent-filter>
959        </receiver> <!-- Broadcast Receiver -->
960        <receiver
960-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:73:9-81:20
961            android:name="app.notifee.core.BlockStateBroadcastReceiver"
961-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:74:13-72
962            android:exported="false" >
962-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:75:13-37
963            <intent-filter>
963-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:76:13-80:29
964                <action android:name="android.app.action.APP_BLOCK_STATE_CHANGED" />
964-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:77:17-85
964-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:77:25-82
965                <action android:name="android.app.action.NOTIFICATION_CHANNEL_BLOCK_STATE_CHANGED" />
965-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:78:17-102
965-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:78:25-99
966                <action android:name="android.app.action.NOTIFICATION_CHANNEL_GROUP_BLOCK_STATE_CHANGED" />
966-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:79:17-108
966-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:79:25-105
967            </intent-filter>
968        </receiver>
969
970        <activity
970-->[com.jakewharton:process-phoenix:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:8:9-12:75
971            android:name="com.jakewharton.processphoenix.ProcessPhoenix"
971-->[com.jakewharton:process-phoenix:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:9:13-73
972            android:exported="false"
972-->[com.jakewharton:process-phoenix:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:10:13-37
973            android:process=":phoenix"
973-->[com.jakewharton:process-phoenix:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:11:13-39
974            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
974-->[com.jakewharton:process-phoenix:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:12:13-72
975
976        <meta-data
976-->[com.facebook.soloader:soloader:0.12.1] F:\R17DevTools\.gradle\caches\8.13\transforms\32503beb4641a05905d0ff4f11c56937\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:12:9-14:37
977            android:name="com.facebook.soloader.enabled"
977-->[com.facebook.soloader:soloader:0.12.1] F:\R17DevTools\.gradle\caches\8.13\transforms\32503beb4641a05905d0ff4f11c56937\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:13:13-57
978            android:value="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
978-->[com.facebook.soloader:soloader:0.12.1] F:\R17DevTools\.gradle\caches\8.13\transforms\32503beb4641a05905d0ff4f11c56937\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:14:13-34
979        <activity
979-->[com.google.android.play:core-common:2.0.3] F:\R17DevTools\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
980            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
980-->[com.google.android.play:core-common:2.0.3] F:\R17DevTools\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
981            android:exported="false"
981-->[com.google.android.play:core-common:2.0.3] F:\R17DevTools\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
982            android:stateNotNeeded="true"
982-->[com.google.android.play:core-common:2.0.3] F:\R17DevTools\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
983            android:theme="@style/Theme.PlayCore.Transparent" />
983-->[com.google.android.play:core-common:2.0.3] F:\R17DevTools\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
984    </application>
985
986</manifest>
