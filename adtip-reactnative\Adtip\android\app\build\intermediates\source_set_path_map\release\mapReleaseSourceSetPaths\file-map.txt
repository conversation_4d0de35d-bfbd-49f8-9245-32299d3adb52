com.adtip.app.adtip_app-res-0 F:\A1\adtip-reactnative\Adtip\android\app\build\generated\crashlytics\res\release
com.adtip.app.adtip_app-res-1 F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets
com.adtip.app.adtip_app-res-2 F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\processReleaseGoogleServices
com.adtip.app.adtip_app-resValues-3 F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\resValues\release
com.adtip.app.adtip_app-packageReleaseResources-4 F:\A1\adtip-reactnative\Adtip\android\app\build\intermediates\incremental\release\packageReleaseResources\merged.dir
com.adtip.app.adtip_app-packageReleaseResources-5 F:\A1\adtip-reactnative\Adtip\android\app\build\intermediates\incremental\release\packageReleaseResources\stripped.dir
com.adtip.app.adtip_app-release-6 F:\A1\adtip-reactnative\Adtip\android\app\build\intermediates\merged_res\release\mergeReleaseResources
com.adtip.app.adtip_app-main-7 F:\A1\adtip-reactnative\Adtip\android\app\src\main\res
com.adtip.app.adtip_app-release-8 F:\A1\adtip-reactnative\Adtip\android\app\src\release\res
com.adtip.app.adtip_app-release-9 F:\A1\adtip-reactnative\Adtip\node_modules\@d11\react-native-fast-image\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-10 F:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-11 F:\A1\adtip-reactnative\Adtip\node_modules\@nozbe\watermelondb\native\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-12 F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-13 F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-clipboard\clipboard\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-14 F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\blur\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-15 F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\datetimepicker\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-16 F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\masked-view\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-17 F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\netinfo\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-18 F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\progress-bar-android\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-19 F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-20 F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-21 F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\auth\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-22 F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-23 F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\database\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-24 F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\firestore\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-25 F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-26 F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\storage\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-27 F:\A1\adtip-reactnative\Adtip\node_modules\@shopify\react-native-skia\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-28 F:\A1\adtip-reactnative\Adtip\node_modules\@videosdk.live\react-native-incallmanager\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-29 F:\A1\adtip-reactnative\Adtip\node_modules\@videosdk.live\react-native-webrtc\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-30 F:\A1\adtip-reactnative\Adtip\node_modules\lottie-react-native\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-31 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-callkeep\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-32 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-compressor\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-33 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-create-thumbnail\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-34 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-date-picker\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-35 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-fs\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-36 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-geolocation-service\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-37 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-gesture-handler\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-38 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-get-random-values\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-39 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-40 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-41 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-42 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-linear-gradient\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-43 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-orientation-locker\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-44 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-pager-view\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-45 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-permissions\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-46 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-razorpay\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-47 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-reanimated\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-48 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-restart\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-49 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-50 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-screens\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-51 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-svg\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-52 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-vector-icons\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-53 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-video\android\buildOutput_a15d4dee7fc4eda61b91308cbb6a2e72\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-54 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-view-shot\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-55 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-vision-camera\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-release-56 F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\packaged_res\release\packageReleaseResources
com.adtip.app.adtip_app-jetified-camera-camera2-1.5.0-alpha03-57 F:\R17DevTools\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\res
com.adtip.app.adtip_app-jetified-drawee-3.6.0-58 F:\R17DevTools\.gradle\caches\8.13\transforms\091d030d0ecfc4218a289f6c4f9c847f\transformed\jetified-drawee-3.6.0\res
com.adtip.app.adtip_app-browser-1.8.0-59 F:\R17DevTools\.gradle\caches\8.13\transforms\0cb78797bbebcc5583f5b3648f5d6ccd\transformed\browser-1.8.0\res
com.adtip.app.adtip_app-jetified-react-android-0.79.2-release-60 F:\R17DevTools\.gradle\caches\8.13\transforms\0dbfeb2f307611649a8567893eebd290\transformed\jetified-react-android-0.79.2-release\res
com.adtip.app.adtip_app-fragment-1.6.1-61 F:\R17DevTools\.gradle\caches\8.13\transforms\1011d3940cd0ae6659aab4410e8bea7d\transformed\fragment-1.6.1\res
com.adtip.app.adtip_app-sqlite-2.3.1-62 F:\R17DevTools\.gradle\caches\8.13\transforms\10cb003b54b62f570d59d1e9b1a41c21\transformed\sqlite-2.3.1\res
com.adtip.app.adtip_app-exifinterface-1.4.1-63 F:\R17DevTools\.gradle\caches\8.13\transforms\169bb1fa3073498b9d2ca913ede4cd56\transformed\exifinterface-1.4.1\res
com.adtip.app.adtip_app-jetified-firebase-crashlytics-19.4.4-64 F:\R17DevTools\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\res
com.adtip.app.adtip_app-jetified-lottie-6.5.2-65 F:\R17DevTools\.gradle\caches\8.13\transforms\2442db8ea23b05bae961f29ce182b42a\transformed\jetified-lottie-6.5.2\res
com.adtip.app.adtip_app-jetified-webpdecoder-********.2-66 F:\R17DevTools\.gradle\caches\8.13\transforms\28d9696d1ba03482c83101bc9186888b\transformed\jetified-webpdecoder-********.2\res
com.adtip.app.adtip_app-transition-1.5.0-67 F:\R17DevTools\.gradle\caches\8.13\transforms\29570d6eb73b370021b74ccbfce09510\transformed\transition-1.5.0\res
com.adtip.app.adtip_app-jetified-camera-extensions-1.5.0-alpha03-68 F:\R17DevTools\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\res
com.adtip.app.adtip_app-jetified-annotation-experimental-1.4.1-69 F:\R17DevTools\.gradle\caches\8.13\transforms\2bb283f821c52533d234058e0f2b4463\transformed\jetified-annotation-experimental-1.4.1\res
com.adtip.app.adtip_app-jetified-play-services-base-18.5.0-70 F:\R17DevTools\.gradle\caches\8.13\transforms\31cc88bd1a811e01477d23dfca8d3955\transformed\jetified-play-services-base-18.5.0\res
com.adtip.app.adtip_app-jetified-ads-adservices-1.1.0-beta11-71 F:\R17DevTools\.gradle\caches\8.13\transforms\32c6230c0475f108e440ef839e9bcc70\transformed\jetified-ads-adservices-1.1.0-beta11\res
com.adtip.app.adtip_app-jetified-viewpager2-1.1.0-72 F:\R17DevTools\.gradle\caches\8.13\transforms\37f3afb509584664f144d26e9d46905a\transformed\jetified-viewpager2-1.1.0\res
com.adtip.app.adtip_app-jetified-core-common-2.0.3-73 F:\R17DevTools\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\res
com.adtip.app.adtip_app-jetified-camera-view-1.5.0-alpha03-74 F:\R17DevTools\.gradle\caches\8.13\transforms\4115c2750b5ddc08fc546f1d52e12846\transformed\jetified-camera-view-1.5.0-alpha03\res
com.adtip.app.adtip_app-jetified-AndroidLame-kotlin-v0.0.1-75 F:\R17DevTools\.gradle\caches\8.13\transforms\43ca8b91167cc3f114f1e31d2dfcc3fd\transformed\jetified-AndroidLame-kotlin-v0.0.1\res
com.adtip.app.adtip_app-sqlite-framework-2.3.1-76 F:\R17DevTools\.gradle\caches\8.13\transforms\44856dc4c7d2bf041bb1d39c0fd95df1\transformed\sqlite-framework-2.3.1\res
com.adtip.app.adtip_app-recyclerview-1.3.1-77 F:\R17DevTools\.gradle\caches\8.13\transforms\48c9b964a381869134ea34daa4e1cac2\transformed\recyclerview-1.3.1\res
com.adtip.app.adtip_app-jetified-datastore-core-release-78 F:\R17DevTools\.gradle\caches\8.13\transforms\5a16c5c2c9cdea968e707ccfe0d81978\transformed\jetified-datastore-core-release\res
com.adtip.app.adtip_app-room-runtime-2.5.2-79 F:\R17DevTools\.gradle\caches\8.13\transforms\5e2acb9bee0a5b872b1d515edf8b6f46\transformed\room-runtime-2.5.2\res
com.adtip.app.adtip_app-jetified-fragment-ktx-1.6.1-80 F:\R17DevTools\.gradle\caches\8.13\transforms\5e9e3cfd885c9b0e37a3b851bad46214\transformed\jetified-fragment-ktx-1.6.1\res
com.adtip.app.adtip_app-jetified-camera-video-1.5.0-alpha03-81 F:\R17DevTools\.gradle\caches\8.13\transforms\6177f3ee616f58e544f5843e29524985\transformed\jetified-camera-video-1.5.0-alpha03\res
com.adtip.app.adtip_app-jetified-tracing-1.2.0-82 F:\R17DevTools\.gradle\caches\8.13\transforms\61c1a68c70ba9e29d9a0551de8cac3e0\transformed\jetified-tracing-1.2.0\res
com.adtip.app.adtip_app-swiperefreshlayout-1.2.0-alpha01-83 F:\R17DevTools\.gradle\caches\8.13\transforms\62bfd621dfe7196adbe33c0a81a67cb9\transformed\swiperefreshlayout-1.2.0-alpha01\res
com.adtip.app.adtip_app-jetified-activity-ktx-1.10.1-84 F:\R17DevTools\.gradle\caches\8.13\transforms\657e3daae72dd7874f0b043c60c6105e\transformed\jetified-activity-ktx-1.10.1\res
com.adtip.app.adtip_app-jetified-play-services-maps-17.0.0-85 F:\R17DevTools\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\res
com.adtip.app.adtip_app-jetified-play-services-wallet-18.1.3-86 F:\R17DevTools\.gradle\caches\8.13\transforms\6a4b12df2937b548059e098326cd7bcc\transformed\jetified-play-services-wallet-18.1.3\res
com.adtip.app.adtip_app-work-runtime-ktx-2.8.1-87 F:\R17DevTools\.gradle\caches\8.13\transforms\6d25c683ba2a5f4e8fc44079c57ede50\transformed\work-runtime-ktx-2.8.1\res
com.adtip.app.adtip_app-jetified-analytics-0.23-88 F:\R17DevTools\.gradle\caches\8.13\transforms\6da9fcb4b3cdec330f337d527696430e\transformed\jetified-analytics-0.23\res
com.adtip.app.adtip_app-jetified-play-services-basement-18.5.0-89 F:\R17DevTools\.gradle\caches\8.13\transforms\6ee598c7bab1dd2859d5d01f2229eaf5\transformed\jetified-play-services-basement-18.5.0\res
com.adtip.app.adtip_app-jetified-credentials-play-services-auth-1.2.0-rc01-90 F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\res
com.adtip.app.adtip_app-lifecycle-livedata-2.8.7-91 F:\R17DevTools\.gradle\caches\8.13\transforms\71f204fe940be86bf3b1a03a3572f444\transformed\lifecycle-livedata-2.8.7\res
com.adtip.app.adtip_app-jetified-ads-identifier-1.0.0-alpha05-92 F:\R17DevTools\.gradle\caches\8.13\transforms\7254cf9bcab09a1b2621dc638a7e5871\transformed\jetified-ads-identifier-1.0.0-alpha05\res
com.adtip.app.adtip_app-jetified-glide-4.15.1-93 F:\R17DevTools\.gradle\caches\8.13\transforms\77a05faf56ea8351de692b50b3edb09d\transformed\jetified-glide-4.15.1\res
com.adtip.app.adtip_app-localbroadcastmanager-1.1.0-94 F:\R17DevTools\.gradle\caches\8.13\transforms\7b3d19fb1cf6c0d27c59fcb1b19cd6b9\transformed\localbroadcastmanager-1.1.0\res
com.adtip.app.adtip_app-webkit-1.11.0-alpha02-95 F:\R17DevTools\.gradle\caches\8.13\transforms\7c0a781ade5f7759726dec1a0903c6ea\transformed\webkit-1.11.0-alpha02\res
com.adtip.app.adtip_app-jetified-emoji2-views-helper-1.3.0-96 F:\R17DevTools\.gradle\caches\8.13\transforms\7ca1dc3deb8725c12fb6d8854d2d457f\transformed\jetified-emoji2-views-helper-1.3.0\res
com.adtip.app.adtip_app-jetified-firebase-messaging-24.1.1-97 F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\res
com.adtip.app.adtip_app-jetified-lifecycle-livedata-core-ktx-2.8.7-98 F:\R17DevTools\.gradle\caches\8.13\transforms\81895437ced6c29dbb31511b38e4b8b0\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\res
com.adtip.app.adtip_app-jetified-play-services-auth-21.3.0-99 F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\res
com.adtip.app.adtip_app-media-1.7.0-100 F:\R17DevTools\.gradle\caches\8.13\transforms\84b7b03ef8446d1de9b5d4ea3cda92f6\transformed\media-1.7.0\res
com.adtip.app.adtip_app-jetified-lifecycle-process-2.8.7-101 F:\R17DevTools\.gradle\caches\8.13\transforms\88ef7ff10d1cfe00bba6c2d4e1214614\transformed\jetified-lifecycle-process-2.8.7\res
com.adtip.app.adtip_app-jetified-startup-runtime-1.1.1-102 F:\R17DevTools\.gradle\caches\8.13\transforms\8ad6a5f68ee830401b49f7ec6ef6ee94\transformed\jetified-startup-runtime-1.1.1\res
com.adtip.app.adtip_app-jetified-media3-ui-1.4.1-103 F:\R17DevTools\.gradle\caches\8.13\transforms\8e1703580f38993d5096d25ba35ecdf1\transformed\jetified-media3-ui-1.4.1\res
com.adtip.app.adtip_app-jetified-media3-exoplayer-1.4.1-104 F:\R17DevTools\.gradle\caches\8.13\transforms\91b5c4a59b6d6a7985119f29ba44ddae\transformed\jetified-media3-exoplayer-1.4.1\res
com.adtip.app.adtip_app-jetified-tracing-ktx-1.2.0-105 F:\R17DevTools\.gradle\caches\8.13\transforms\939b3454ba58eaee81c8299edbbed6b3\transformed\jetified-tracing-ktx-1.2.0\res
com.adtip.app.adtip_app-constraintlayout-2.1.4-106 F:\R17DevTools\.gradle\caches\8.13\transforms\951972483b6ed7761be36fb1794da2fa\transformed\constraintlayout-2.1.4\res
com.adtip.app.adtip_app-jetified-customview-poolingcontainer-1.0.0-107 F:\R17DevTools\.gradle\caches\8.13\transforms\96ea896024c6120d1a1a85dce56aede2\transformed\jetified-customview-poolingcontainer-1.0.0\res
com.adtip.app.adtip_app-coordinatorlayout-1.2.0-108 F:\R17DevTools\.gradle\caches\8.13\transforms\971aefb1c45b63a51a59784e2f6f0f2e\transformed\coordinatorlayout-1.2.0\res
com.adtip.app.adtip_app-jetified-media3-session-1.4.1-109 F:\R17DevTools\.gradle\caches\8.13\transforms\9eda951e918e947e7d9ddd1ba9b2c563\transformed\jetified-media3-session-1.4.1\res
com.adtip.app.adtip_app-jetified-firebase-common-21.0.0-110 F:\R17DevTools\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\res
com.adtip.app.adtip_app-jetified-ads-adservices-java-1.1.0-beta11-111 F:\R17DevTools\.gradle\caches\8.13\transforms\a74c60921ee11cceb07fcadaf42c9d30\transformed\jetified-ads-adservices-java-1.1.0-beta11\res
com.adtip.app.adtip_app-jetified-lifecycle-viewmodel-savedstate-2.8.7-112 F:\R17DevTools\.gradle\caches\8.13\transforms\a8feaa743bf52883b5bccbe748b774d2\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\res
com.adtip.app.adtip_app-jetified-datastore-preferences-release-113 F:\R17DevTools\.gradle\caches\8.13\transforms\a97a0244ead91994bae9d4a822ce48b2\transformed\jetified-datastore-preferences-release\res
com.adtip.app.adtip_app-jetified-activity-1.10.1-114 F:\R17DevTools\.gradle\caches\8.13\transforms\b0d63cb00ac6bfd545ea575ceef8be11\transformed\jetified-activity-1.10.1\res
com.adtip.app.adtip_app-core-runtime-2.2.0-115 F:\R17DevTools\.gradle\caches\8.13\transforms\b24c3eb51e3ed594d8ad409d854e8433\transformed\core-runtime-2.2.0\res
com.adtip.app.adtip_app-jetified-core-ktx-1.16.0-116 F:\R17DevTools\.gradle\caches\8.13\transforms\b6209f910c1bc01c6bfd83d02a0a0d1e\transformed\jetified-core-ktx-1.16.0\res
com.adtip.app.adtip_app-jetified-ucrop-2.2.10-117 F:\R17DevTools\.gradle\caches\8.13\transforms\b7e10b62d187296d864392ee64da0a65\transformed\jetified-ucrop-2.2.10\res
com.adtip.app.adtip_app-jetified-room-ktx-2.5.2-118 F:\R17DevTools\.gradle\caches\8.13\transforms\bf21f91f640e4132fe219ebff54b55a7\transformed\jetified-room-ktx-2.5.2\res
com.adtip.app.adtip_app-jetified-emoji2-1.3.0-119 F:\R17DevTools\.gradle\caches\8.13\transforms\c02c6a58638664cbfa4f398f77214d5c\transformed\jetified-emoji2-1.3.0\res
com.adtip.app.adtip_app-jetified-autofill-1.1.0-120 F:\R17DevTools\.gradle\caches\8.13\transforms\c071ca435d7e4157d1f8663601ef1e49\transformed\jetified-autofill-1.1.0\res
com.adtip.app.adtip_app-jetified-BlurView-version-2.0.4-121 F:\R17DevTools\.gradle\caches\8.13\transforms\c769ee9e6d014a389db47ef5f7f593e0\transformed\jetified-BlurView-version-2.0.4\res
com.adtip.app.adtip_app-lifecycle-livedata-core-2.8.7-122 F:\R17DevTools\.gradle\caches\8.13\transforms\c9708445cb3f39759eddbf7eb8a24dfa\transformed\lifecycle-livedata-core-2.8.7\res
com.adtip.app.adtip_app-drawerlayout-1.1.1-123 F:\R17DevTools\.gradle\caches\8.13\transforms\c9effbe9a9224ae8f0b56f94b90e2693\transformed\drawerlayout-1.1.1\res
com.adtip.app.adtip_app-jetified-play-services-ads-24.3.0-124 F:\R17DevTools\.gradle\caches\8.13\transforms\c9fc1356237c73a36541687fd82ee2b5\transformed\jetified-play-services-ads-24.3.0\res
com.adtip.app.adtip_app-jetified-camera-core-1.5.0-alpha03-125 F:\R17DevTools\.gradle\caches\8.13\transforms\cab56218d538a3aaadf18b40d63683ba\transformed\jetified-camera-core-1.5.0-alpha03\res
com.adtip.app.adtip_app-jetified-lifecycle-runtime-release-126 F:\R17DevTools\.gradle\caches\8.13\transforms\cd47cb17e395838b1ad8102f427d6548\transformed\jetified-lifecycle-runtime-release\res
com.adtip.app.adtip_app-work-runtime-2.8.1-127 F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\res
com.adtip.app.adtip_app-jetified-exoplayer-core-2.19.1-128 F:\R17DevTools\.gradle\caches\8.13\transforms\d457dd3000de28855e41f930745b41e3\transformed\jetified-exoplayer-core-2.19.1\res
com.adtip.app.adtip_app-jetified-lifecycle-viewmodel-ktx-2.8.7-129 F:\R17DevTools\.gradle\caches\8.13\transforms\d75396e545ff5e8b6de70384498a4e0f\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\res
com.adtip.app.adtip_app-jetified-appcompat-resources-1.7.0-130 F:\R17DevTools\.gradle\caches\8.13\transforms\d8f3cb842f708b4d9f96a19d0863fec6\transformed\jetified-appcompat-resources-1.7.0\res
com.adtip.app.adtip_app-jetified-core-202108261754-131 F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\res
com.adtip.app.adtip_app-jetified-lifecycle-runtime-ktx-release-132 F:\R17DevTools\.gradle\caches\8.13\transforms\d90ca647b7e079a9cca20ce5e0695cd1\transformed\jetified-lifecycle-runtime-ktx-release\res
com.adtip.app.adtip_app-jetified-core-viewtree-1.0.0-133 F:\R17DevTools\.gradle\caches\8.13\transforms\db21677b3a01819e80addb86a0500cda\transformed\jetified-core-viewtree-1.0.0\res
com.adtip.app.adtip_app-jetified-savedstate-1.2.1-134 F:\R17DevTools\.gradle\caches\8.13\transforms\dc4429a347bd69bf2e56511963168424\transformed\jetified-savedstate-1.2.1\res
com.adtip.app.adtip_app-material-1.12.0-135 F:\R17DevTools\.gradle\caches\8.13\transforms\dd11809084d63593099820b405d61701\transformed\material-1.12.0\res
com.adtip.app.adtip_app-jetified-play-services-ads-api-24.3.0-136 F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\res
com.adtip.app.adtip_app-jetified-credentials-1.2.0-rc01-137 F:\R17DevTools\.gradle\caches\8.13\transforms\dfbd6dfbd7eb9930845c16c988ee8525\transformed\jetified-credentials-1.2.0-rc01\res
com.adtip.app.adtip_app-core-1.16.0-138 F:\R17DevTools\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\res
com.adtip.app.adtip_app-jetified-ads-identifier-common-1.0.0-alpha05-139 F:\R17DevTools\.gradle\caches\8.13\transforms\eb805fa6f99a9860490971d2cec19714\transformed\jetified-ads-identifier-common-1.0.0-alpha05\res
com.adtip.app.adtip_app-jetified-datastore-release-140 F:\R17DevTools\.gradle\caches\8.13\transforms\edf5a19ba2adf1c85091121ced6da3d9\transformed\jetified-datastore-release\res
com.adtip.app.adtip_app-appcompat-1.7.0-141 F:\R17DevTools\.gradle\caches\8.13\transforms\ef18ad19ff26599d64ec0eff4ea7dc70\transformed\appcompat-1.7.0\res
com.adtip.app.adtip_app-jetified-savedstate-ktx-1.2.1-142 F:\R17DevTools\.gradle\caches\8.13\transforms\efc34c31fe060b184c0432cf507aff39\transformed\jetified-savedstate-ktx-1.2.1\res
com.adtip.app.adtip_app-jetified-standard-core-1.6.53-143 F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\res
com.adtip.app.adtip_app-jetified-offerwall-1.0.11-144 F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\res
com.adtip.app.adtip_app-jetified-camera-lifecycle-1.5.0-alpha03-145 F:\R17DevTools\.gradle\caches\8.13\transforms\f46551b3f95a282437058c04b2b8602c\transformed\jetified-camera-lifecycle-1.5.0-alpha03\res
com.adtip.app.adtip_app-jetified-profileinstaller-1.4.0-146 F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\res
com.adtip.app.adtip_app-jetified-exoplayer-ui-2.19.1-147 F:\R17DevTools\.gradle\caches\8.13\transforms\f9b6829f7f71dcf7ed8c3a43ef2febe5\transformed\jetified-exoplayer-ui-2.19.1\res
com.adtip.app.adtip_app-cardview-1.0.0-148 F:\R17DevTools\.gradle\caches\8.13\transforms\fa89a3c9324ea04452492f3cae338a2f\transformed\cardview-1.0.0\res
com.adtip.app.adtip_app-jetified-lifecycle-service-2.8.7-149 F:\R17DevTools\.gradle\caches\8.13\transforms\facb7e7221e6d5866717d8be3b94d9bd\transformed\jetified-lifecycle-service-2.8.7\res
com.adtip.app.adtip_app-jetified-lifecycle-viewmodel-release-150 F:\R17DevTools\.gradle\caches\8.13\transforms\fb667b71c56725e04f12fbd8df400c00\transformed\jetified-lifecycle-viewmodel-release\res
