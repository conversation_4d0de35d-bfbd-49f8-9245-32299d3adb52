# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 52ms
  generate-prefab-packages
    exec-prefab 735ms
    [gap of 16ms]
  generate-prefab-packages completed in 755ms
  execute-generate-process
    exec-configure 985ms
    [gap of 159ms]
  execute-generate-process completed in 1144ms
  [gap of 13ms]
  write-metadata-json-to-file 11ms
generate_cxx_metadata completed in 1976ms

