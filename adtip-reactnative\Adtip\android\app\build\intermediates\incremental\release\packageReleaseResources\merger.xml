<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res"><file name="ic_call" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable\ic_call.xml" qualifiers="" type="drawable"/><file name="ic_call_end" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable\ic_call_end.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_mic_off" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable\ic_mic_off.xml" qualifiers="" type="drawable"/><file name="ic_volume_up" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable\ic_volume_up.xml" qualifiers="" type="drawable"/><file name="rn_edit_text_material" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable\rn_edit_text_material.xml" qualifiers="" type="drawable"/><file name="assets_images_logo" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-mdpi\assets_images_logo.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_backicon" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-mdpi\node_modules_reactnavigation_elements_lib_module_assets_backicon.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_backiconmask" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-mdpi\node_modules_reactnavigation_elements_lib_module_assets_backiconmask.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_clearicon" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-mdpi\node_modules_reactnavigation_elements_lib_module_assets_clearicon.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_closeicon" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-mdpi\node_modules_reactnavigation_elements_lib_module_assets_closeicon.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_searchicon" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-mdpi\node_modules_reactnavigation_elements_lib_module_assets_searchicon.png" qualifiers="mdpi-v4" type="drawable"/><file name="src_assets_gpayicon_icons8googlepay48" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-mdpi\src_assets_gpayicon_icons8googlepay48.png" qualifiers="mdpi-v4" type="drawable"/><file name="src_assets_images_defaultavatar" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-mdpi\src_assets_images_defaultavatar.png" qualifiers="mdpi-v4" type="drawable"/><file name="src_assets_images_default_profile" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-mdpi\src_assets_images_default_profile.png" qualifiers="mdpi-v4" type="drawable"/><file name="src_assets_images_logo" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-mdpi\src_assets_images_logo.png" qualifiers="mdpi-v4" type="drawable"/><file name="src_assets_images_onboarding1" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-mdpi\src_assets_images_onboarding1.png" qualifiers="mdpi-v4" type="drawable"/><file name="src_assets_images_onboarding2" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-mdpi\src_assets_images_onboarding2.png" qualifiers="mdpi-v4" type="drawable"/><file name="src_assets_images_onboarding3" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-mdpi\src_assets_images_onboarding3.png" qualifiers="mdpi-v4" type="drawable"/><file name="src_assets_phonepeicon_icons8phonepe48" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-mdpi\src_assets_phonepeicon_icons8phonepe48.png" qualifiers="mdpi-v4" type="drawable"/><file name="src_assets_upi_icon_upi" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-mdpi\src_assets_upi_icon_upi.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_backicon" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-xhdpi\node_modules_reactnavigation_elements_lib_module_assets_backicon.png" qualifiers="xhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_clearicon" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-xhdpi\node_modules_reactnavigation_elements_lib_module_assets_clearicon.png" qualifiers="xhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_closeicon" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-xhdpi\node_modules_reactnavigation_elements_lib_module_assets_closeicon.png" qualifiers="xhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_searchicon" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-xhdpi\node_modules_reactnavigation_elements_lib_module_assets_searchicon.png" qualifiers="xhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_backicon" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-xxhdpi\node_modules_reactnavigation_elements_lib_module_assets_backicon.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_clearicon" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-xxhdpi\node_modules_reactnavigation_elements_lib_module_assets_clearicon.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_closeicon" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-xxhdpi\node_modules_reactnavigation_elements_lib_module_assets_closeicon.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_searchicon" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-xxhdpi\node_modules_reactnavigation_elements_lib_module_assets_searchicon.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_backicon" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-xxxhdpi\node_modules_reactnavigation_elements_lib_module_assets_backicon.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_clearicon" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-xxxhdpi\node_modules_reactnavigation_elements_lib_module_assets_clearicon.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_closeicon" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-xxxhdpi\node_modules_reactnavigation_elements_lib_module_assets_closeicon.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_searchicon" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\drawable-xxxhdpi\node_modules_reactnavigation_elements_lib_module_assets_searchicon.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="activity_incoming_call" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\layout\activity_incoming_call.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\values\colors.xml" qualifiers=""><color name="colorPrimary">#24d05a</color><color name="colorPrimaryDark">#1eb54a</color><color name="colorAccent">#00C853</color><color name="statusBarColor">#FFFFFF</color><color name="backgroundColor">#F8FAFC</color><color name="cardBackground">#FFFFFF</color><color name="textPrimary">#0F172A</color><color name="textSecondary">#374151</color><color name="textTertiary">#6B7280</color><color name="iconColor">#374151</color><color name="borderColor">#D1D5DB</color><color name="rippleColor">#20000000</color><color name="red">#FF0000</color><color name="call_green">#4CAF50</color><color name="call_red">#F44336</color><color name="call_blue">#2196F3</color><color name="call_orange">#FF9800</color><color name="call_background">#000000</color><color name="call_overlay">#80000000</color><color name="white">#FFFFFF</color><color name="black">#000000</color><color name="transparent">#00000000</color></file><file path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Adtip</string></file><file path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
        <item name="android:windowBackground">@color/backgroundColor</item>
        <item name="android:statusBarColor">@color/statusBarColor</item>
        <item name="android:textColor">@color/textPrimary</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:enforceNavigationBarContrast">false</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style></file><file path="F:\A1\adtip-reactnative\Adtip\android\app\src\main\res\values-night\colors.xml" qualifiers="night-v8"><color name="colorPrimary">#24d05a</color><color name="colorPrimaryDark">#1eb54a</color><color name="colorAccent">#00C853</color><color name="statusBarColor">#000000</color><color name="backgroundColor">#000000</color><color name="cardBackground">#000000</color><color name="textPrimary">#FFFFFF</color><color name="textSecondary">#E2E8F0</color><color name="textTertiary">#CBD5E1</color><color name="iconColor">#CBD5E1</color><color name="borderColor">#333333</color><color name="rippleColor">#80FFFFFF</color></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\A1\adtip-reactnative\Adtip\android\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\A1\adtip-reactnative\Adtip\android\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\resValues\release"/><source path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets"/><source path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\processReleaseGoogleServices"/><source path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\crashlytics\res\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\resValues\release"><file path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\resValues\release\values\gradleResValues.xml" qualifiers=""><integer name="react_native_dev_server_port">8081</integer></file></source><source path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets"><file name="assets_icon_app_logo" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\assets_icon_app_logo.png" qualifiers="mdpi-v4" type="drawable"/><file name="assets_images_logo" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\assets_images_logo.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_cpxresearchsdkreactnative_assets_clock" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_cpxresearchsdkreactnative_assets_clock.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_cpxresearchsdkreactnative_assets_close" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_cpxresearchsdkreactnative_assets_close.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_cpxresearchsdkreactnative_assets_help" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_cpxresearchsdkreactnative_assets_help.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_cpxresearchsdkreactnative_assets_home" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_cpxresearchsdkreactnative_assets_home.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_cpxresearchsdkreactnative_assets_settings" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_cpxresearchsdkreactnative_assets_settings.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_cpxresearchsdkreactnative_assets_star" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_cpxresearchsdkreactnative_assets_star.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_backicon" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_reactnavigation_elements_lib_module_assets_backicon.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_backiconmask" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_reactnavigation_elements_lib_module_assets_backiconmask.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_clearicon" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_reactnavigation_elements_lib_module_assets_clearicon.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_closeicon" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_reactnavigation_elements_lib_module_assets_closeicon.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_searchicon" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\node_modules_reactnavigation_elements_lib_module_assets_searchicon.png" qualifiers="mdpi-v4" type="drawable"/><file name="src_assets_gifs_congratulation" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\src_assets_gifs_congratulation.gif" qualifiers="mdpi-v4" type="drawable"/><file name="src_assets_gpayicon_icons8googlepay48" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\src_assets_gpayicon_icons8googlepay48.png" qualifiers="mdpi-v4" type="drawable"/><file name="src_assets_images_adtiplogofinal" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\src_assets_images_adtiplogofinal.png" qualifiers="mdpi-v4" type="drawable"/><file name="src_assets_images_default_profile" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\src_assets_images_default_profile.png" qualifiers="mdpi-v4" type="drawable"/><file name="src_assets_images_logo" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\src_assets_images_logo.png" qualifiers="mdpi-v4" type="drawable"/><file name="src_assets_phonepeicon_icons8phonepe48" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\src_assets_phonepeicon_icons8phonepe48.png" qualifiers="mdpi-v4" type="drawable"/><file name="src_assets_upi_icon_upi" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-mdpi\src_assets_upi_icon_upi.png" qualifiers="mdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_backicon" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xhdpi\node_modules_reactnavigation_elements_lib_module_assets_backicon.png" qualifiers="xhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_clearicon" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xhdpi\node_modules_reactnavigation_elements_lib_module_assets_clearicon.png" qualifiers="xhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_closeicon" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xhdpi\node_modules_reactnavigation_elements_lib_module_assets_closeicon.png" qualifiers="xhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_searchicon" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xhdpi\node_modules_reactnavigation_elements_lib_module_assets_searchicon.png" qualifiers="xhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_backicon" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xxhdpi\node_modules_reactnavigation_elements_lib_module_assets_backicon.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_clearicon" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xxhdpi\node_modules_reactnavigation_elements_lib_module_assets_clearicon.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_closeicon" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xxhdpi\node_modules_reactnavigation_elements_lib_module_assets_closeicon.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_searchicon" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xxhdpi\node_modules_reactnavigation_elements_lib_module_assets_searchicon.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_backicon" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xxxhdpi\node_modules_reactnavigation_elements_lib_module_assets_backicon.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_clearicon" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xxxhdpi\node_modules_reactnavigation_elements_lib_module_assets_clearicon.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_closeicon" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xxxhdpi\node_modules_reactnavigation_elements_lib_module_assets_closeicon.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="node_modules_reactnavigation_elements_lib_module_assets_searchicon" path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\createBundleReleaseJsAndAssets\drawable-xxxhdpi\node_modules_reactnavigation_elements_lib_module_assets_searchicon.png" qualifiers="xxxhdpi-v4" type="drawable"/></source><source path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\processReleaseGoogleServices"><file path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\res\processReleaseGoogleServices\values\values.xml" qualifiers=""><string name="default_web_client_id" translatable="false">333436486029-24e4ub781j4i3fl1k1p2e52amno8pmrp.apps.googleusercontent.com</string><string name="gcm_defaultSenderId" translatable="false">333436486029</string><string name="google_api_key" translatable="false">AIzaSyBamdG5TwWa4VjiVHo1rk4zgyMWHE4JQJc</string><string name="google_app_id" translatable="false">1:333436486029:android:5957954f40f53a01217dc4</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyBamdG5TwWa4VjiVHo1rk4zgyMWHE4JQJc</string><string name="google_storage_bucket" translatable="false">adtip-3873c.appspot.com</string><string name="project_id" translatable="false">adtip-3873c</string></file></source><source path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\crashlytics\res\release"><file path="F:\A1\adtip-reactnative\Adtip\android\app\build\generated\crashlytics\res\release\values\com_google_firebase_crashlytics_mappingfileid.xml" qualifiers=""><string name="com.google.firebase.crashlytics.mapping_file_id" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">00000000000000000000000000000000</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-createBundleReleaseJsAndAssets$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-createBundleReleaseJsAndAssets" generated-set="res-createBundleReleaseJsAndAssets$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processReleaseGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processReleaseGoogleServices" generated-set="res-processReleaseGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="legacy_api_res$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="legacy_api_res" generated-set="legacy_api_res$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>